<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="30 seconds">
    <!-- 统一日志路径 -->
    <springProperty name="appName" source="spring.application.name" defaultValue="default-app"/>
    <property name="LOG_HOME" value="logs/${appName}"/>
    <property name="LOG_PATTERN"
              value="%d{yyyy-MM-dd HH:mm:ss.SSS} %5p [${appName},%X{traceId:-},%X{spanId:-}] [%15.15t] %-40.40logger{39} : %m%n"/>

    <!-- Color log definition -->
    <property name="CONSOLE_LOG_PATTERN"
              value="${CONSOLE_LOG_PATTERN:-%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} [%X{tid}] %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"
    />
    <conversionRule conversionWord="clr" class="org.springframework.boot.logging.logback.ColorConverter"/>
    <conversionRule conversionWord="wex"
                    class="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter"/>
    <conversionRule conversionWord="wEx"
                    class="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter"/>

    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
        </encoder>
    </appender>
    <appender name="GRPC-LOG" class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.log.GRPCLogClientAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.mdc.TraceIdMDCPatternLogbackLayout">
                <Pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%X{tid}] [%thread] %-5level %logger{36} -%msg%n</Pattern>
            </layout>
        </encoder>
    </appender>

    <!-- 文件滚动策略 - 暂时注释掉避免与Nacos日志冲突 -->
    <!--    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">-->
    <!--        <file>${LOG_HOME}.log</file>-->
    <!--        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">-->
    <!--            <fileNamePattern>${LOG_HOME}-%d{yyyy-MM-dd}.%i.log</fileNamePattern>-->
    <!--            <maxFileSize>100MB</maxFileSize>-->
    <!--            <maxHistory>30</maxHistory>-->
    <!--        </rollingPolicy>-->
    <!--        <encoder>-->
    <!--            <pattern>${LOG_PATTERN}</pattern>-->
    <!--        </encoder>-->
    <!--    </appender>-->

    <!-- 异步日志 - 暂时注释掉避免文件冲突 -->
    <!--    <appender name="ASYNC_FILE" class="ch.qos.logback.classic.AsyncAppender">-->
    <!--        <queueSize>1000</queueSize>-->
    <!--        <discardingThreshold>0</discardingThreshold>-->
    <!--        <appender-ref ref="FILE"/>-->
    <!--    </appender>-->

    <!-- 日志级别配置 -->
    <root level="INFO">
        <appender-ref ref="GRPC-LOG"/>
        <appender-ref ref="CONSOLE"/>
    </root>

    <!-- 第三方库日志 -->
    <logger name="org.reflections.Reflections" level="OFF"/>

    <!-- Nacos 日志配置 - 禁用内部日志配置，避免冲突 -->
    <logger name="com.alibaba.nacos" level="WARN" additivity="false">
        <appender-ref ref="CONSOLE"/>
    </logger>
    <logger name="com.alibaba.nacos.client.config" level="WARN" additivity="false">
        <appender-ref ref="CONSOLE"/>
    </logger>
    <logger name="com.alibaba.nacos.client.naming" level="WARN" additivity="false">
        <appender-ref ref="CONSOLE"/>
    </logger>

    <!-- 其他第三方库日志 -->
    <logger name="org.apache.http" level="WARN"/>
    <logger name="org.springframework.data.redis" level="WARN"/>
    <logger name="org.mybatis" level="WARN"/>
</configuration>
