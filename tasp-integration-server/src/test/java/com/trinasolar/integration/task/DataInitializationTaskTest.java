package com.trinasolar.integration.task;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.trinasolar.integration.api.entity.AppSystem;
import com.trinasolar.integration.api.entity.ApplicationProgram;
import com.trinasolar.integration.api.entity.ApplicationProgramChange;
import com.trinasolar.integration.api.entity.InteAppSystemChange;
import com.trinasolar.integration.api.mapper.ApplicationProgramChangeMapper;
import com.trinasolar.integration.api.mapper.InteAppSystemChangeMapper;
import com.trinasolar.integration.dao.AppSystemMapper;
import com.trinasolar.integration.dao.ApplicationProgramMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.boot.ApplicationArguments;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * @className: DataInitializationTaskTest
 * @Description: 数据初始化任务测试类
 * @author: pengshy
 * @date: 2025/9/3
 */
@ExtendWith(MockitoExtension.class)
class DataInitializationTaskTest {

    @Mock
    private RedissonClient redissonClient;

    @Mock
    private InteAppSystemChangeMapper appSystemChangeMapper;

    @Mock
    private ApplicationProgramChangeMapper appProgramChangeMapper;

    @Mock
    private AppSystemMapper appSystemMapper;

    @Mock
    private ApplicationProgramMapper appProgramMapper;

    @Mock
    private RLock rLock;

    @Mock
    private ApplicationArguments applicationArguments;

    @InjectMocks
    private DataInitializationTask dataInitializationTask;

    @BeforeEach
    void setUp() {
        when(redissonClient.getLock(anyString())).thenReturn(rLock);
    }

    @Test
    void testRunWithLockAcquired() throws Exception {
        // 模拟获取锁成功
        when(rLock.tryLock(anyLong(), anyLong(), any(TimeUnit.class))).thenReturn(true);
        when(rLock.isHeldByCurrentThread()).thenReturn(true);

        // 模拟应用系统数据
        AppSystem appSystem = new AppSystem();
        appSystem.setId(1L);
        appSystem.setCode("TEST_SYSTEM");
        when(appSystemMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Arrays.asList(appSystem));

        // 模拟变更表中无数据
        when(appSystemChangeMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // 模拟应用程序数据
        ApplicationProgram program = new ApplicationProgram();
        program.setId(1L);
        program.setProgramCode("TEST_PROGRAM");
        program.setSystemId("1");
        when(appProgramMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Arrays.asList(program));

        // 模拟变更表中无数据
        when(appProgramChangeMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // 执行测试
        dataInitializationTask.run(applicationArguments);

        // 验证插入操作被调用
        verify(appSystemChangeMapper, times(1)).insert(any(InteAppSystemChange.class));
        verify(appProgramChangeMapper, times(1)).insert(any(ApplicationProgramChange.class));
        verify(rLock, times(1)).unlock();
    }

    @Test
    void testRunWithLockNotAcquired() throws Exception {
        // 模拟获取锁失败
        when(rLock.tryLock(anyLong(), anyLong(), any(TimeUnit.class))).thenReturn(false);

        // 执行测试
        dataInitializationTask.run(applicationArguments);

        // 验证没有执行初始化操作
        verify(appSystemChangeMapper, never()).insert(any(InteAppSystemChange.class));
        verify(appProgramChangeMapper, never()).insert(any(ApplicationProgramChange.class));
        verify(rLock, never()).unlock();
    }

    @Test
    void testRunWithExistingData() throws Exception {
        // 模拟获取锁成功
        when(rLock.tryLock(anyLong(), anyLong(), any(TimeUnit.class))).thenReturn(true);
        when(rLock.isHeldByCurrentThread()).thenReturn(true);

        // 模拟应用系统数据
        AppSystem appSystem = new AppSystem();
        appSystem.setId(1L);
        appSystem.setCode("TEST_SYSTEM");
        when(appSystemMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Arrays.asList(appSystem));

        // 模拟变更表中已有数据


        // 模拟应用程序数据
        ApplicationProgram program = new ApplicationProgram();
        program.setId(1L);
        program.setProgramCode("TEST_PROGRAM");
        program.setSystemId("1");
        when(appProgramMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Arrays.asList(program));

        // 执行测试
        dataInitializationTask.run(applicationArguments);

        // 验证没有插入操作（因为数据已存在）
        verify(appSystemChangeMapper, never()).insert(any(InteAppSystemChange.class));
        verify(appProgramChangeMapper, never()).insert(any(ApplicationProgramChange.class));
        verify(rLock, times(1)).unlock();
    }

    @Test
    void testRunWithEmptySourceData() throws Exception {
        // 模拟获取锁成功
        when(rLock.tryLock(anyLong(), anyLong(), any(TimeUnit.class))).thenReturn(true);
        when(rLock.isHeldByCurrentThread()).thenReturn(true);

        // 模拟源表中无数据
        when(appSystemMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());
        when(appProgramMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // 执行测试
        dataInitializationTask.run(applicationArguments);

        // 验证没有插入操作（因为源表无数据）
        verify(appSystemChangeMapper, never()).insert(any(InteAppSystemChange.class));
        verify(appProgramChangeMapper, never()).insert(any(ApplicationProgramChange.class));
        verify(rLock, times(1)).unlock();
    }
}
