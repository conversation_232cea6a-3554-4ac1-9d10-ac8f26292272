<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trinasolar.integration.dao.UserAppMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.trinasolar.integration.api.entity.UserApp">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="user_code" property="userCode" jdbcType="VARCHAR"/>
        <result column="app_id" property="appId" jdbcType="BIGINT"/>
        <result column="mark_type" property="markType" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, user_id, app_id, mark_type, create_time
    </sql>

    <!-- 扩展列（包含用户编码） -->
    <sql id="Extended_Column_List">
        tasp_base.sys_user_app.id,
        tasp_base.sys_user_app.user_id,
        tasp_base.sys_user_app.app_id,
        tasp_base.sys_user_app.mark_type,
        tasp_base.sys_user_app.create_time,
        tasp_base.sys_user.user_code
    </sql>

    <!-- 根据应用ID查询用户应用关系 -->
    <select id="getUserAppByAppId" resultMap="BaseResultMap">
        SELECT
        <include refid="Extended_Column_List"/>
        FROM tasp_base.sys_user_app
        LEFT JOIN tasp_base.sys_user ON tasp_base.sys_user_app.user_id = tasp_base.sys_user.id
        WHERE tasp_base.sys_user_app.app_id = #{appId}
    </select>

    <!-- 根据应用ID列表查询用户应用关系 -->
    <select id="selectUserByAppIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Extended_Column_List"/>
        FROM tasp_base.sys_user_app
        LEFT JOIN tasp_base.sys_user ON tasp_base.sys_user_app.user_id = tasp_base.sys_user.id
        <where>
            <if test="appIds != null and appIds.size() > 0">
                tasp_base.sys_user_app.app_id IN
                <foreach collection="appIds" item="appId" open="(" separator="," close=")">
                    #{appId}
                </foreach>
            </if>
        </where>
    </select>

</mapper>
