package com.trinasolar.integration.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Maps;
import com.trinasolar.integration.api.entity.ProjectConfigDO;
import com.trinasolar.integration.controller.projectconfig.vo.ProjectConfigSaveReqVO;
import com.trinasolar.integration.dao.ProjectConfigMapper;
import com.trinasolar.integration.service.ProjectConfigService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.trinasolar.integration.util.ProjectConfigConstants.*;


/**
 * 项目配置信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ProjectConfigServiceImpl implements ProjectConfigService {


    @Resource
    private ProjectConfigMapper projectConfigMapper;

    /**
     * 查询应用系统devops初始是否完成
     *
     * @param appSystemId
     * @return
     */
    @Override
    public Boolean initCheck(Long appSystemId) {
        List<ProjectConfigDO> projectConfigDOS = projectConfigMapper.selectByProjectId(appSystemId);
        return CollectionUtil.isNotEmpty(projectConfigDOS) && projectConfigDOS.size() >= 3;
    }

    @Override
    public ProjectConfigDO selectProjectConfigByProjectId(Long projectId) {
        return projectConfigMapper.selectProjectConfigByProjectId(projectId);
    }

    @Override
    public ProjectConfigDO selectById(Long id) {
        return projectConfigMapper.selectById(id);
    }

    @Override
    public ProjectConfigDO selectByProjectIdAndConfigKey(ProjectConfigSaveReqVO reqVO) {
        return projectConfigMapper.selectByProjectIdAndConfigKey(reqVO);
    }

    /**
     * 保存流水线分组信息
     *
     * @param envs
     * @param systemId
     */
    @Override
    public void addProjectEnvConfigs(Map<String, String> envs, Long systemId) {

        ProjectConfigDO basicConfigDO = new ProjectConfigDO();
        basicConfigDO.setConfigContent(JSONUtil.toJsonStr(envs));
        basicConfigDO.setProjectId(systemId);
        basicConfigDO.setConfigKey(PROJECT_DEVOPS_ENV_INFO_KEY);
        basicConfigDO.setConfigName("项目流水线环境信息");
        basicConfigDO.setStatus(0);
        basicConfigDO.setIsDefault(1);
        projectConfigMapper.insert(basicConfigDO);

    }

    /**
     * 保存namespace信息和devops项目信息
     * <p>
     * 保存项目（应用系统）配置信息 - 优化为批量插入
     */
    @Override
    public void saveProjectConfig(String originData, Long systemId, String busDomain) {
        List<ProjectConfigDO> configList = new ArrayList<>();

        //加入DevOps项目配置信息
        ProjectConfigDO configDO = new ProjectConfigDO();
        configDO.setConfigContent(originData);
        configDO.setProjectId(systemId);
        configDO.setConfigKey(DEVOPS_BASIC_INFO_KEY);
        configDO.setConfigName("BizDevOps基础信息");
        configDO.setStatus(0);
        configDO.setIsDefault(1);
        configList.add(configDO);

        //加入项目配置信息
        ProjectConfigDO basicConfigDO = new ProjectConfigDO();
        Map<String, String> projectConfigJson = Maps.newHashMap();
        //k8s namespace，实际替换流水线json文件中[TEAM_NAME]，这里用甲方确认使用业务域替换TEAM_NAME，
        //完整namespace规则为teamname--projectname--{env}，devops会自动拼接并创建namespace
        projectConfigJson.put("namespace", busDomain);
        basicConfigDO.setConfigContent(JSONUtil.toJsonStr(projectConfigJson));
        basicConfigDO.setProjectId(systemId);
        basicConfigDO.setConfigKey(PROJECT_BASIC_INFO_KEY);
        basicConfigDO.setConfigName("项目基础信息");
        basicConfigDO.setStatus(0);
        basicConfigDO.setIsDefault(1);
        configList.add(basicConfigDO);

        // 批量插入配置信息
        configList.forEach(projectConfigMapper::insert);
    }
}
