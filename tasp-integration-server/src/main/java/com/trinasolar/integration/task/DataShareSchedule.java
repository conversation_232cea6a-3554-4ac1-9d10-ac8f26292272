package com.trinasolar.integration.task;

import com.trinasolar.integration.service.DataShareService;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import com.trinasolar.integration.service.impl.DataShareServiceImpl;

import java.util.concurrent.TimeUnit;

/**
 * @className: DataShareSchedule
 * @Description: 应用系统&应用程序数据共享：定时推送版本变更数据到kafka
 * @author: pengshy
 * @date: 2025/9/2 16:50
 */

@RefreshScope
@Component
@Slf4j
public class DataShareSchedule {

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private DataShareService dataShareService;

    @Scheduled(cron = "${schedule.datashare.system-increment-cron}")
    public void executeDataShareIncrement() {
        log.info("执行应用系统增量共享任务");
        // 1. 获取分布式锁（使用项目统一Redis前缀）
        RLock taskLock = redissonClient.getLock("tasp:datashare:system:increment:task:lock");

        try {
            // 2. 尝试获取锁（0秒等待，30秒自动释放，防止死锁）
            boolean isLocked = taskLock.tryLock(0, 30, TimeUnit.SECONDS);
            if (isLocked) {
                // 3. 执行任务逻辑（调用现有Service层方法）
                dataShareService.executeDataShareIncrement();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } finally {
            // 4. 确保锁释放（只释放当前线程持有的锁）
            if (taskLock.isHeldByCurrentThread()) {
                taskLock.unlock();
            }
        }
        log.info("执行应用系统增量共享任务结束");
    }

    @Scheduled(cron = "${schedule.datashare.program-increment-cron}")
    public void executeDataShareProgramIncrement() {
        log.info("执行应用程序增量共享任务");
        // 1. 获取分布式锁（使用项目统一Redis前缀）
        RLock taskLock = redissonClient.getLock("tasp:datashare:program:increment:task:lock");

        try {
            // 2. 尝试获取锁（0秒等待，30秒自动释放，防止死锁）
            boolean isLocked = taskLock.tryLock(0, 30, TimeUnit.SECONDS);
            if (isLocked) {
                // 3. 执行任务逻辑（调用现有Service层方法）
                dataShareService.executeDataShareProgramIncrement();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } finally {
            // 4. 确保锁释放（只释放当前线程持有的锁）
            if (taskLock.isHeldByCurrentThread()) {
                taskLock.unlock();
            }
        }
        log.info("执行应用程序增量共享任务结束");
    }

    @Scheduled(cron = "${schedule.datashare.system-full-cron}")
    public void executeDataShareFull() {
        log.info("执行应用系统全量共享任务");
        // 1. 获取分布式锁（使用项目统一Redis前缀）
        RLock taskLock = redissonClient.getLock("tasp:datashare:system:full:task:lock");

        try {
            // 2. 尝试获取锁（0秒等待，30秒自动释放，防止死锁）
            boolean isLocked = taskLock.tryLock(0, 30, TimeUnit.SECONDS);
            if (isLocked) {
                // 3. 执行任务逻辑（调用现有Service层方法）
                dataShareService.executeDataShareFull();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } finally {
            // 4. 确保锁释放（只释放当前线程持有的锁）
            if (taskLock.isHeldByCurrentThread()) {
                taskLock.unlock();
            }
        }
        log.info("执行应用系统全量共享任务结束");
    }


    @Scheduled(cron = "${schedule.datashare.program-full-cron}")
    public void executeDataShareProgramFull() {
        log.info("执行应用程序全量共享任务");
        // 1. 获取分布式锁（使用项目统一Redis前缀）
        RLock taskLock = redissonClient.getLock("tasp:datashare:program:full:task:lock");

        try {
            // 2. 尝试获取锁（0秒等待，30秒自动释放，防止死锁）
            boolean isLocked = taskLock.tryLock(0, 30, TimeUnit.SECONDS);
            if (isLocked) {
                // 3. 执行任务逻辑（调用现有Service层方法）
                dataShareService.executeDataShareProgramFull();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } finally {
            // 4. 确保锁释放（只释放当前线程持有的锁）
            if (taskLock.isHeldByCurrentThread()) {
                taskLock.unlock();
            }
        }
        log.info("执行应用程序全量共享任务结束");
    }
}
