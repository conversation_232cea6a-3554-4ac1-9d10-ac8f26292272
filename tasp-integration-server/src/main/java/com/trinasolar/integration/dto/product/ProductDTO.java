package com.trinasolar.integration.dto.product;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 产品信息数据传输对象
 * <p>
 * 用于在系统间传递产品相关数据
 *
 * <AUTHOR>
 */
@Data
public class ProductDTO {
    /**
     * 产品唯一标识ID
     */
    private Long id;

    /**
     * 产品相对ID
     */
    private Long productId;

    /**
     * 产品名称
     */
    private String name;

    /**
     * 产品详细描述
     */
    private String description;

    /**
     * 产品相关URL链接
     */
    private String url;


    /**
     * 产品相关类别 用于跳转
     */
    private String type;

    /**
     * 产品供应商信息
     */
    private String supplier;

    /**
     * 产品分类
     */
    private String category;

    /**
     * 产品版本号
     */
    private String version;


    /**
     * logo url
     */
    private String logoUrl;

    /**
     * 创建人
     */
    private String creator;


    /**
     * 更新人
     */
    private String updater;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;


    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 文件类型
     */
    private String fileType;

}
