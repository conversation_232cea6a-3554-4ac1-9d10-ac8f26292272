package com.trinasolar.integration.api.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.trinasolar.integration.api.dto.CategoryDocmentDTO;
import com.trinasolar.integration.api.entity.ProductServicePO;
import com.trinasolar.integration.api.entity.ShareComponentDO;
import com.trinasolar.integration.api.knowledge.ISpaceProvider;
import com.trinasolar.integration.api.mapper.ProductServiceMapper;
import com.trinasolar.integration.api.resq.R;
import com.trinasolar.integration.api.service.ProductService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class ProductServiceImpl extends ServiceImpl<ProductServiceMapper, ProductServicePO> implements ProductService {

    @Autowired
    private ISpaceProvider keplerKnowledgeClient;
    @Override
    public List<ProductServicePO> getAllProducts() {
        return list();
    }

    @Override
    public int addComponentToProduct(ShareComponentDO shareComponentDO) {
        ProductServicePO productServicePO = new ProductServicePO();
        productServicePO.setName(shareComponentDO.getComponentName());
        productServicePO.setDescription(shareComponentDO.getDescription());
        productServicePO.setUrl(shareComponentDO.getFileUrl());
        productServicePO.setLogoUrl(shareComponentDO.getLogoUrl());
        productServicePO.setType("共享组件");
        productServicePO.setCategory("共享组件");
        productServicePO.setVersion(shareComponentDO.getVersion());
        productServicePO.setComponentId(shareComponentDO.getId());
        productServicePO.setUpdater(shareComponentDO.getUpdater());
        productServicePO.setCreator(shareComponentDO.getCreator());
        productServicePO.setCreateTime(shareComponentDO.getCreateTime());
        productServicePO.setUpdateTime(shareComponentDO.getUpdateTime());
        productServicePO.setFileType(shareComponentDO.getFileType());
        return save(productServicePO) ? 1 : 0;
    }

    @Override
    public int updateComponentToProduct(ShareComponentDO shareComponentDO) {
        ProductServicePO productServicePO = baseMapper.selectOne(new QueryWrapper<ProductServicePO>().eq("component_id", shareComponentDO.getId()));
        if (Objects.nonNull(productServicePO)) {
            productServicePO.setName(shareComponentDO.getComponentName());
            productServicePO.setDescription(shareComponentDO.getDescription());
            productServicePO.setUrl(shareComponentDO.getFileUrl());
            productServicePO.setLogoUrl(shareComponentDO.getLogoUrl());
            productServicePO.setVersion(shareComponentDO.getVersion());
            productServicePO.setFileType(shareComponentDO.getFileType());
            productServicePO.setUpdater(shareComponentDO.getUpdater());
            productServicePO.setUpdateTime(shareComponentDO.getUpdateTime());
            return baseMapper.update(productServicePO, new QueryWrapper<ProductServicePO>().eq("component_id", shareComponentDO.getId()));
        }
        return 0;
    }

    @Override
    public int deleteComponentToProduct(Long id) {
        return baseMapper.delete(new QueryWrapper<ProductServicePO>().eq("component_id", id));
    }

    @Async
    @Override
    public void syncComponentDoc(ShareComponentDO shareComponentDO) {

        try {
            // 开始时间
            long startTime = System.currentTimeMillis();
            // 先指定目录id
            CategoryDocmentDTO document = new CategoryDocmentDTO();
            R<Long> result = keplerKnowledgeClient.getCatalog();
            if (Objects.nonNull(result) && Objects.nonNull(result.getData())) {
                document.setCategoriesIds(String.valueOf(result.getData()));
                document.setAlias(shareComponentDO.getComponentName());
                document.setDescription(shareComponentDO.getDescription());
                document.setIsRelease(1);
                document.setIsRag(1);
                document.setKeyWords(shareComponentDO.getComponentName());
                document.setDocumentId(shareComponentDO.getFileId());
                keplerKnowledgeClient.add(document);
            }
            // 结束时间
            long endTime = System.currentTimeMillis();
            // 转换为s
            log.info("同步组件文档耗时:{}s", (endTime - startTime) / 1000);
        }catch (Exception e) {
            log.warn("同步组件文档失败,共享组件信息:{}", JSON.toJSON(shareComponentDO), e);
        }
    }
}
