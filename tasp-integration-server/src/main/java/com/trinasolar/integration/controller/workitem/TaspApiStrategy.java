package com.trinasolar.integration.controller.workitem;

public class TaspApiStrategy implements WorkItemApiStrategy{

    private String apiType;

    public TaspApiStrategy(String apiType) {
        this.apiType = apiType;
    }
    @Override
    public WorkItemPage<WorkItemBO> processApi() {
        // 根据不同的API类型，调用不同的接口
        if ("subscription".equals(apiType)) {
            System.out.println("调用API订阅申请接口，处理申请...");
            return null;
        } else if ("publish".equals(apiType)) {
            System.out.println("调用API发布申请接口，处理申请...");
            return null;
        } else {
            System.out.println("未知的API申请类型");
            return null;
        }
    }
}
