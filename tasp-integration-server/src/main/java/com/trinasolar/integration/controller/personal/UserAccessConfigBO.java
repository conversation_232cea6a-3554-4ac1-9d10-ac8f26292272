package com.trinasolar.integration.controller.personal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户快捷入口配置表
 *
 * <AUTHOR>
 */
@Data
public class UserAccessConfigBO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 类型（1：管理员配置；2：开发者配置；3：个性化配置）
     */
    private Integer accessType;

    /**
     * logo地址
     */
    private String logoUrl;

    /**
     * 名称
     */
    private String name;

    /**
     * 网址
     */
    private String url;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    private String updater;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 是否删除：0：不删除，1：删除
     */
    private Integer deleted;
}
