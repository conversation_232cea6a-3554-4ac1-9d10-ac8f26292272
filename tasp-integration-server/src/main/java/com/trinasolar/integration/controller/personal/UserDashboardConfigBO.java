package com.trinasolar.integration.controller.personal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户工作台配置表
 *
 * <AUTHOR>
 */
@Data
public class UserDashboardConfigBO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 模块显隐JSON
     */
    private String moduleShow;

    /**
     * 工作事项标签页显隐JSON
     */
    private String workItem;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    private String updater;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 是否删除：0：不删除，1：删除
     */
    private Integer deleted;
}
