package com.trinasolar.integration.dao;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trinasolar.integration.api.entity.ComponentLogoDO;
import com.trinasolar.integration.api.entity.UserAccessConfigDO;
import com.trinasolar.integration.controller.component.ComponentLogoBO;
import com.trinasolar.integration.controller.personal.NoticeBO;
import com.trinasolar.integration.controller.personal.PageReq;
import com.trinasolar.integration.controller.personal.UserAccessConfigBO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface UserAccessConfigMapper extends BaseMapper<UserAccessConfigDO> {
    int deleteByUserId(String userId);

    int insertBatch(List<UserAccessConfigBO> list);

    List<UserAccessConfigBO> selectByUserId(String userId);

    List<UserAccessConfigBO> selectDefaultConfig(int accessType);

    Page<NoticeBO> noticePage(Page<NoticeBO> page, @Param("req") PageReq req);
}
