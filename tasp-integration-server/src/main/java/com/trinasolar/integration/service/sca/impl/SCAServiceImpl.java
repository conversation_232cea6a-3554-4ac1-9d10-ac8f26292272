package com.trinasolar.integration.service.sca.impl;

import com.alibaba.fastjson.JSONObject;
import com.trinasolar.integration.service.sca.SCAHandleService;
import com.trinasolar.integration.util.RequestUtils;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;
import java.util.UUID;

/**
 * 参数维护工具类
 *
 * <AUTHOR>
 */
@Service("SCAService")
public class SCAServiceImpl implements SCAHandleService {

    // 5分钟，单位毫秒
    private static final String DEFAULT_SIGNATURE_VERSION = "1.0";
    private static final String DEFAULT_SIGNATURE_METHOD = "HMAC-SHA256";

    /**
     * 访问密钥
     */
    @Value("${sca.access-key:68a52aa99d399eb54ed1d218}")
    private String accessKey;

    @Value("${sca.secret-key:12ce30bd-bd87-4f86-81e1-b3067f36ef31}")
    private String secretKey;


    /**
     * 生成并维护签名参数
     *
     * @return 包含签名参数的Map
     */
    @Override
    public Map<String, String> generateSignatureParams(JSONObject payloadObject, Map<String, String> requestParams) {
        Map<String, String> params = new HashMap<>();
        // 使用默认签名版本
        params.put("SignatureVersion", DEFAULT_SIGNATURE_VERSION);
        // 使用默认签名方法
        params.put("SignatureMethod", DEFAULT_SIGNATURE_METHOD);
        Long timestamp = Instant.now().getEpochSecond();
        // 生成当前时间戳
        params.put("Timestamp", String.valueOf(timestamp));
        // 生成随机Nonce
        String signatureNonce = UUID.randomUUID().toString();
        params.put("SignatureNonce", signatureNonce);
        // 设置访问密钥
        params.put("AccessKey", accessKey);
        params.put("Signature", generateSignature(timestamp, signatureNonce, payloadObject, requestParams));
        return params;
    }


    public String generateSignature(long timestamp,
                                    String signatureNonce,
                                    JSONObject payloadObject, Map<String, String> requestParams) {
        String payload = "";
        if (payloadObject != null) {
            payload = payloadObject.toString();
        }
        String httpMethod = RequestUtils.getRequest().getMethod();
        // 构建标准查询字符串
        String canonicalQueryString = buildCanonicalQueryString(timestamp,
                signatureNonce, accessKey, requestParams);

        // 计算Payload哈希
        String hashedPayload = calculatePayloadHash(payload);

        // 构建String2Sign
        String string2Sign = buildString2Sign(timestamp, httpMethod, canonicalQueryString, hashedPayload);

        // 计算HMAC-SHA256签名并进行Base64编码
        return calculateHmacSha256(string2Sign, secretKey);
    }


    /**
     * 构建标准查询字符串CanonicalizedQueryString
     */
    private String buildCanonicalQueryString(long timestamp,
                                             String signatureNonce, String accessKey, Map<String, String> requestParams) {
        // 创建参数列表（不包含Signature参数）
        Map<String, String> allParams = new TreeMap<>();

        // 添加公共请求参数
        allParams.put("Timestamp", String.valueOf(timestamp));
        allParams.put("SignatureVersion", DEFAULT_SIGNATURE_VERSION);
        allParams.put("SignatureMethod", DEFAULT_SIGNATURE_METHOD);
        allParams.put("SignatureNonce", signatureNonce);
        allParams.put("AccessKey", accessKey);

        // 添加请求参数
        if (requestParams != null) {
            allParams.putAll(requestParams);
        }

        // 构建标准查询字符串
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, String> entry : allParams.entrySet()) {
            if (sb.length() > 0) {
                sb.append("&");
            }
            sb.append(urlEncode(entry.getKey())).append("=")
                    .append(urlEncode(entry.getValue()));
        }

        return sb.toString();
    }

    /**
     * 计算Payload哈希值
     */
    private String calculatePayloadHash(String payload) {
        try {
            // 处理大文件上传情况（小于4k字节取全部，否则取前4k字节）
            String processedPayload = payload;
            if (processedPayload != null && processedPayload.length() > 4096) {
                processedPayload = processedPayload.substring(0, 4096);
            } else if (processedPayload == null) {
                processedPayload = "";
            }

            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] bytes = digest.digest(processedPayload.getBytes(StandardCharsets.UTF_8));

            StringBuilder sb = new StringBuilder();
            String temp;
            for (byte aByte : bytes) {
                temp = Integer.toHexString(aByte & 0xFF);
                if (temp.length() == 1) {
                    //1得到一位的进行补0操作
                    sb.append("0");
                }
                sb.append(temp);
            }
            return sb.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("Failed to calculate payload hash", e);
        }
    }

    /**
     * 构建String2Sign
     */
    private String buildString2Sign(long timestamp, String httpMethod, String canonicalQueryString, String hashedPayload) {
        StringBuilder sb = new StringBuilder();
        sb.append(timestamp).append('\n');
        sb.append(httpMethod.toUpperCase()).append('\n');
        sb.append(canonicalQueryString).append('\n');
        sb.append(hashedPayload).append("1");
        return sb.toString();
    }

    /**
     * URL编码
     */
    private String urlEncode(String value) {
        try {
            return java.net.URLEncoder.encode(value, StandardCharsets.UTF_8)
                    .replace("+", "%20")
                    .replace("*", "%2A")
                    .replace("%7E", "~");
        } catch (Exception e) {
            throw new RuntimeException("Failed to URL encode value: " + value, e);
        }
    }

    /**
     * 计算HMAC-SHA256签名
     */
    private String calculateHmacSha256(String data, String key) {
        try {
            Mac mac = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKeySpec = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            mac.init(secretKeySpec);
            byte[] hash = mac.doFinal(data.getBytes(StandardCharsets.UTF_8));
            return Base64.encodeBase64String(hash);
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            throw new RuntimeException("Failed to calculate HMAC-SHA256 signature", e);
        }
    }

}
