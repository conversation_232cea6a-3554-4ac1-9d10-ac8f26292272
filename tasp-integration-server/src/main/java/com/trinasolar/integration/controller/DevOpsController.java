package com.trinasolar.integration.controller;

import cn.hutool.json.JSONObject;
import com.alibaba.fastjson.JSON;
import com.trinasolar.integration.api.vo.AppGitlabSaveVO;
import com.trinasolar.integration.api.vo.PipelineRespVO;
import com.trinasolar.integration.api.vo.UpdateMemberDTO;
import com.trinasolar.integration.controller.devops.vo.DevOpsPipelineBuildLogRespVO;
import com.trinasolar.integration.controller.devops.vo.DevOpsPipelineHisRespVO;
import com.trinasolar.integration.controller.devops.vo.DevOpsProjectRespVO;
import com.trinasolar.integration.service.DevOpsProjectService;
import com.trinasolar.integration.service.PipelineService;
import com.trinasolar.integration.service.ProjectService;
import com.trinasolar.tasc.framework.common.pojo.CommonResult;
import com.trinasolar.tasc.framework.common.pojo.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.trinasolar.tasc.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - DevOps API")
@RestController
@RequestMapping("/devops")
@Validated
public class DevOpsController {
    /**
     * //查询某个应用的流水线
     * https://bizdevops.trinasolar.com/ms/process/api/user/pipelines/projects/x9549d/listViewPipelines?showDelete=true&sortType=LAST_EXEC_TIME&collation=DESC&page=1&pageSize=50&filterByPipelineName=testapi111003&viewId=allPipeline
     * //获取构建历史
     * https://bizdevops.trinasolar.com/ms/process/api/user/builds/x9549d/p-b8e6bfd3dd5242889f00efe009200552/history/new?page=1&pageSize=5&
     * //获取某次流水线的构建日志
     * https://bizdevops.trinasolar.com/ms/log/api/user/logs/x9549d/p-b8e6bfd3dd5242889f00efe009200552/b-228f4bdc6515436887b011b19173b763?executeCount=1&subTag=&debug=false
     * //获取构建参数
     * https://bizdevops.trinasolar.com/ms/process/api/user/builds/x9549d/p-b8e6bfd3dd5242889f00efe009200552/manualStartupInfo
     * //获取流水线定义
     * https://bizdevops.trinasolar.com/ms/process/api/user/pipelines/x9549d/p-b8e6bfd3dd5242889f00efe009200552
     * //开始构建流水线（POST）
     * https://bizdevops.trinasolar.com/ms/process/api/user/builds/x9549d/p-b8e6bfd3dd5242889f00efe009200552?buildNo=1
     * //启动参数
     * https://bizdevops.trinasolar.com/ms/process/api/user/builds/x9549d/p-b8e6bfd3dd5242889f00efe009200552/b-546d303630b84250b4055ac8dcdbdaf9/parameters
     */

    @Resource
    private ProjectService projectService;

    @Resource
    private PipelineService pipelineService;

    @Resource
    private DevOpsProjectService devOpsProjectService;


    @GetMapping("/pipelines")
    @Operation(summary = "获取流水线信息")
    @Parameter(name = "programId", description = "应用程序ID", required = true)
    public CommonResult<PageResult<PipelineRespVO>> getPipelines(@RequestParam(value = "programId") Long programId) {
        return success(pipelineService.getPipelines(programId));
    }

    @GetMapping("/getExistProject")
    @Operation(summary = "获取单个流水线信息")
    @Parameter(name = "programId", description = "应用程序ID", required = true)
    public Object getExistProject(@RequestParam(value = "projectName") String projectName) {
        return JSON.parse(devOpsProjectService.getExistProject(projectName).getOriginData());
    }


    @GetMapping("/pipelines/deploy")
    @Operation(summary = "流水线部署")
    public CommonResult<JSONObject> deploy(@RequestParam(value = "projectId") Long projectId, @RequestParam(value = "pipelineId") String pipelineId) {
        return success(pipelineService.deploy(projectId, pipelineId));

    }

    @DeleteMapping("/pipelines/revoke")
    @Operation(summary = "流水线移除权限")
    public CommonResult<Boolean> revoke(@RequestParam(value = "projectId") Long projectId, @RequestParam(value = "appName") String appName, @RequestParam(value = "userCodes") List<String> userCodes) {
        return success(pipelineService.revoke(projectId, appName, userCodes));

    }

    @GetMapping("/pipelines/grant")
    @Operation(summary = "流水线授权")
    public CommonResult<Boolean> grant(@RequestParam(value = "projectId") Long projectId, @RequestParam(value = "appName") String appName, @RequestParam(value = "userCodes") List<String> userCodes) {
        return success(pipelineService.grant(projectId, appName, userCodes));
    }

//    @PutMapping("/member/increment")
//    @Operation(summary = "devops产品（项目）新增成员")
//    @Parameter(name = "projectCode", description = "devops产品（项目）编码", required = true)
//    @Parameter(name = "userCodes", description = "用户编码（多个用逗号分隔）", required = true)
//    @Parameter(name = "userNames", description = "用户git账号（多个用逗号分隔）", required = true)
//    public JSONObject incrementMember4Project(@RequestParam(value = "projectCode") String projectCode, @RequestParam(value = "userCodes") String userCodes) {
//        return JSONUtil.parseObj(devOpsProjectService.incrementMember4Project(projectCode, userCodes));
//    }
//
//
//    @DeleteMapping("/member/remove")
//    @Operation(summary = "devops产品（项目）删除成员")
//    @Parameter(name = "projectCode", description = "devops产品（项目）编码", required = true)
//    @Parameter(name = "userCode", description = "用户编码", required = true)
//    public JSONObject removeMember4Project(@RequestParam(value = "projectCode") String projectCode, @RequestParam(value = "userCode") String userCode) {
//        return JSONUtil.parseObj(devOpsProjectService.removeMember4Project(projectCode, userCode));
//    }


    @PostMapping("/member/update")
    public CommonResult<Boolean> updateMember4Project(@RequestBody UpdateMemberDTO updateMemberDTO) {
        devOpsProjectService.updateMember4Project(updateMemberDTO);
        return CommonResult.success(Boolean.TRUE);
    }

//    @PostMapping("/test")
//    public CommonResult<Boolean> test(@RequestParam(value = "systemId")Long systemId, @RequestParam(value = "delCodes")List<String> delCodes, @RequestParam(value = "incrCodes")List<String> incrCodes) {
//        devOpsProjectService.updateProject(systemId, delCodes, incrCodes);
//        return CommonResult.success(Boolean.TRUE);
//    }


//    @Resource
//    private GitService gitService;
//
//    @PutMapping("/create/gitlab/group")
//    @Operation(summary = " 创建gitlab group")
//    public GitGroupRespVO appSystemList(@RequestParam(value = "groupName") String groupName) {
//        return gitService.createGitGroup(groupName);
//    }


    @GetMapping("/pipelines/history")
    @Operation(summary = "获取流水线构建执行记录")
    @Parameter(name = "projectCode", description = "DevOps项目编号", required = true, example = "1024")
    @Parameter(name = "pipelineId", description = "流水线编号", required = true, example = "1024")
    public CommonResult<PageResult<DevOpsPipelineHisRespVO>> getPipelinesHistory(
            @RequestParam("projectCode") String projectCode, @RequestParam("pipelineId") String pipelineId) {
        return success(pipelineService.getPipelinesHistory(projectCode, pipelineId));
    }

    @GetMapping("/pipelines/logs")
    @Operation(summary = "获取流水线执行记录日志")
    @Parameter(name = "projectCode", description = "DevOps项目编号", required = true, example = "1024")
    @Parameter(name = "pipelineId", description = "流水线编号", required = true, example = "1024")
    @Parameter(name = "buildId", description = "构建编号", required = true, example = "1024")
    public CommonResult<DevOpsPipelineBuildLogRespVO> getPipelinesLogs(@RequestParam("projectCode") String projectCode,
                                                                       @RequestParam("pipelineId") String pipelineId, @RequestParam("buildId") String buildId) {
        return success(pipelineService.getPipelinesBuildLogs(projectCode, pipelineId, buildId));
    }

    @GetMapping("/deploy/detail")
    @Operation(summary = "获取流水线详情")
    @Parameter(name = "projectCode", description = "DevOps项目编号", required = true, example = "1024")
    @Parameter(name = "pipelineId", description = "流水线编号", required = true, example = "1024")
    public CommonResult<JSONObject> getDeployDetail(@RequestParam("projectCode") String projectCode,
                                                    @RequestParam("pipelineId") String pipelineId) {
        return success(pipelineService.pipelineDetails(projectCode, pipelineId));
    }


    @DeleteMapping("/pipelines/{programId}")
    @Operation(summary = "删除流水线")
    @Parameter(name = "programId", description = "应用程序id", required = true)
    public CommonResult<Boolean> deletePipelines(@PathVariable Long programId) {
        return success(pipelineService.deletePipelines(programId));
    }

    @GetMapping("/maven/setting")
    @Operation(summary = "添加maven setting[测试验证用]")
    @Parameter(name = "projectId", description = "项目id", required = true)
    public CommonResult<Boolean> addMavenSetting(@RequestParam("projectId") String projectId) {
        return success(devOpsProjectService.addMavenSetting(projectId));
    }
}
