package com.trinasolar.integration.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trinasolar.integration.api.entity.FlowInstanceDO;
import com.trinasolar.integration.api.entity.UserApp;
import com.trinasolar.integration.controller.personal.FlowInstanceCountBO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface FlowInstanceMapper extends BaseMapper<FlowInstanceDO> {

    @Select("(select COUNT(instance_name) as instanceCount, instance_name ,business_type from tasp_base.flow_instance WHERE instance_status != 'REJECTED' AND business_type = 'api_order' GROUP BY instance_name " +
            "ORDER BY instanceCount DESC LIMIT 5) " +
            "UNION ALL " +
            "(select COUNT(instance_name) as instanceCount, instance_name ,business_type from tasp_base.flow_instance WHERE instance_status != 'REJECTED' AND business_type = 'paas_server' GROUP BY instance_name " +
            "ORDER BY instanceCount DESC LIMIT 5) ")
    List<FlowInstanceCountBO> getFlowInstanceCount();
}
