<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trinasolar.integration.dao.UserDashboardConfigMapper">
    <delete id="deleteByUserId">
        DELETE FROM tasp_user_dashboard_config WHERE user_id = #{userId}
    </delete>
    <select id="selectByUserId" resultType="com.trinasolar.integration.api.entity.UserDashboardConfigDO">
        SELECT * FROM tasp_user_dashboard_config WHERE user_id = #{userId} limit 1
    </select>
</mapper>
