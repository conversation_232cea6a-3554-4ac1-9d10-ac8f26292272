package com.trinasolar.integration.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Auth：cao.meng
 * @Date：2024/11/25 9:43
 */
@Data
@TableName(value = "tasp_base.flow_instance")
public class FlowInstanceDO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(value = "instance_id", type = IdType.INPUT)
    private String instanceId;

    private String instanceName;
    /**
     * 来源
     */
    private String sourceName;
    /**
     * 流程id
     */
    private String processId;

    private String processName;
    /**
     * 业务单据id
     */
    private String businessId;
    /**
     * 业务单据类型
     */
    private String businessType;
    /**
     * 流程状态,审批中-APPROVING，REJECTED-拒绝、APPROVE-通过
     */
    private String instanceStatus;
    /**
     * 发起人id
     */
    private String initiatorId;
    /**
     * 发起人
     */
    private String initiatorName;
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    /**
     * 业务数据
     */
    private String businessData;
    /**
     * 扩展字段
     */
    private String extendFields;

    /**
     * 默认记录创建时间字段，新建时自动填充
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    /**
     * 默认记录创建人ID字段，新建时自动填充
     */
    @TableField(fill = FieldFill.INSERT)
    private Long creatorId;

    /**
     * 默认记录创建人姓名字段，新建时自动填充
     */
    @TableField(fill = FieldFill.INSERT)
    private String creatorName;

    /**
     * 默认记录修改时间字段，新建修改时自动填充
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;

    /**
     * 默认记录修改人ID字段，新建修改时自动填充
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updaterId;

    /**
     * 默认记录修改人姓名字段，新建修改时自动填充
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updaterName;

    /**
     * 默认逻辑删除标记，is_deleted=0有效，新建时由数据库赋值0
     */
    @TableLogic
    @JsonIgnore
    @TableField(value = "is_deleted", select = false)
    private Integer deleted;


    @TableField(value = "tenant_id")
    private Integer tenantId;
}
