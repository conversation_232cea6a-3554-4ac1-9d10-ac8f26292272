<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trinasolar.integration.dao.ShareComponentMapper">
    <update id="updateStatus">
        update tasp_share_component set status = #{status} where id = #{id}
    </update>
    <update id="deleteComponentById">
        update tasp_share_component set deleted = 1 where id = #{id}
    </update>

    <select id="shareComponentPage"
            resultType="com.trinasolar.integration.controller.component.ShareComponentVO">
        SELECT * FROM tasp_share_component WHERE deleted = 0
        <if test="componentName != null and componentName != '' ">
            AND component_name LIKE CONCAT('%', #{componentName}, '%')
        </if>
    </select>
    <select id="checkComponentNameExists" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM tasp_share_component WHERE deleted = 0
        <if test="id != null">
            AND id = #{id}
        </if>
        <if test="componentName != null and componentName != '' ">
            AND component_name = #{componentName}
        </if>
    </select>
    <select id="checkUpdateComponentNameExists" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM tasp_share_component WHERE deleted = 0
        <if test="id != null">
            AND id != #{id}
        </if>
        <if test="componentName != null and componentName != '' ">
            AND component_name = #{componentName}
        </if>
    </select>
</mapper>
