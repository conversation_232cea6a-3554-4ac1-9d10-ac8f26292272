package com.trinasolar.integration.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trinasolar.integration.api.dto.User;
import com.trinasolar.integration.controller.personal.NoticeBO;
import com.trinasolar.integration.controller.personal.PageReq;
import com.trinasolar.integration.controller.personal.UerPersonalConfigBO;
import com.trinasolar.integration.controller.personal.UerPersonalInfoBO;
import com.trinasolar.integration.controller.workitem.WorkItemBO;
import com.trinasolar.integration.controller.workitem.WorkItemPage;
import com.trinasolar.integration.controller.workitem.WorkItemReq;


public interface PersonalWorkspaceService {
    User getUserInfo();

    boolean saveUserConfig(UerPersonalConfigBO uerPersonalConfigBO);

    UerPersonalConfigBO getUserConfig(String userId);

    UerPersonalInfoBO getPersonalWorkspace();

    Page<NoticeBO> noticePage(Page<NoticeBO> page, PageReq req);

    WorkItemPage<WorkItemBO> getPersonalWorkItem(WorkItemReq req);
}
