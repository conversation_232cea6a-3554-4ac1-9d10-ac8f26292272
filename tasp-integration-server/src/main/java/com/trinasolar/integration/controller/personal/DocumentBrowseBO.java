package com.trinasolar.integration.controller.personal;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Description 文档浏览
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DocumentBrowseBO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 文档id
     */
    private Long id;

    /**
     * 文档名称
     */
    private String alias;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private String createBy;

}
