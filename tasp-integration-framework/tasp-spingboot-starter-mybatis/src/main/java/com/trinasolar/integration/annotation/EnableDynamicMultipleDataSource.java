package com.trinasolar.integration.annotation;

import com.trinasolar.integration.TsDynamicDataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.context.annotation.Import;

import java.lang.annotation.*;

/**
 * 开启动态数据源
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
@Import(TsDynamicDataSourceAutoConfiguration.class)
public @interface EnableDynamicMultipleDataSource {
}
