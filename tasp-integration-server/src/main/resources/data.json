{\\n  \\\"swagger\\\": \\\"2.0\\\",\\n  \\\"info\\\": {\\n    \\\"x-ibm-name\\\": \\\"apitest0922\\\",\\n    \\\"title\\\": \\\"API发布测试DEV\\\",\\n    \\\"version\\\": \\\"1.0\\\",\\n    \\\"description\\\": \\\"Api Documentation\\\"\\n  },\\n  \\\"schemes\\\": [\\n    \\\"https\\\"\\n  ],\\n  \\\"basePath\\\": \\\"/\\\",\\n  \\\"consumes\\\": [\\n    \\\"application/json\\\"\\n  ],\\n  \\\"produces\\\": [\\n    \\\"application/json\\\"\\n  ],\\n  \\\"securityDefinitions\\\": {\\n    \\\"TSL-ClientID\\\": {\\n      \\\"type\\\": \\\"apiKey\\\",\\n      \\\"in\\\": \\\"header\\\",\\n      \\\"name\\\": \\\"tsl-clientid\\\",\\n      \\\"x-key-type\\\": \\\"client_id\\\"\\n    },\\n    \\\"TSL-ClientSecret\\\": {\\n      \\\"type\\\": \\\"apiKey\\\",\\n      \\\"in\\\": \\\"header\\\",\\n      \\\"name\\\": \\\"tsl-clientsecret\\\",\\n      \\\"x-key-type\\\": \\\"client_secret\\\"\\n    }\\n  },\\n  \\\"x-ibm-configuration\\\": {\\n    \\\"assembly\\\": {\\n      \\\"catch\\\": [],\\n      \\\"execute\\\": [\\n        {\\n          \\\"gatewayscript\\\": {\\n            \\\"source\\\": \\\"// Copyright IBM Corp. Year 2020, 2021\\\\nvar apim = require('apim')\\\\nvar pathObj = apim.getvariable('request.path')\\\\nvar remainPath = pathObj.substring(pathObj.indexOf('/',1),(pathObj.length))\\\\nvar searchObj = apim.getvariable('request.search')\\\\napim.setvariable('message.headers.tsltoken','123456')\\\\napim.setvariable('remainPath',remainPath )\\\",\\n            \\\"title\\\": \\\"gatewayscript\\\",\\n            \\\"version\\\": \\\"2.0.0\\\"\\n          }\\n        },\\n        {\\n          \\\"invoke\\\": {\\n            \\\"backend-type\\\": \\\"detect\\\",\\n            \\\"cache-response\\\": \\\"protocol\\\",\\n            \\\"cache-ttl\\\": 900,\\n            \\\"chunked-uploads\\\": false,\\n            \\\"graphql-send-type\\\": \\\"detect\\\",\\n            \\\"header-control\\\": {\\n              \\\"type\\\": \\\"blocklist\\\",\\n              \\\"values\\\": []\\n            },\\n            \\\"http-version\\\": \\\"HTTP/1.1\\\",\\n            \\\"parameter-control\\\": {\\n              \\\"type\\\": \\\"blocklist\\\",\\n              \\\"values\\\": []\\n            },\\n            \\\"stop-on-error\\\": [],\\n            \\\"target-url\\\": \\\"$(target-url)$(request.path)\\\",\\n            \\\"timeout\\\": 60,\\n            \\\"title\\\": \\\"invoke\\\",\\n            \\\"verb\\\": \\\"keep\\\",\\n            \\\"version\\\": \\\"2.2.0\\\",\\n            \\\"websocket-upgrade\\\": false\\n          }\\n        }\\n      ]\\n    },\\n    \\\"enforced\\\": true,\\n    \\\"testable\\\": true,\\n    \\\"phase\\\": \\\"realized\\\",\\n    \\\"cors\\\": {\\n      \\\"enabled\\\": true\\n    },\\n    \\\"properties\\\": {\\n      \\\"target-url\\\": {\\n        \\\"description\\\": \\\"请替换缺省值为实际后端服务的真实URL\\\",\\n        \\\"encoded\\\": false,\\n        \\\"value\\\": \\\"http://localhost:18089\\\"\\n      }\\n    },\\n    \\\"gateway\\\": \\\"datapower-api-gateway\\\",\\n    \\\"activity-log\\\": {\\n      \\\"enabled\\\": true,\\n      \\\"error-content\\\": \\\"payload\\\",\\n      \\\"success-content\\\": \\\"activity\\\"\\n    },\\n    \\\"type\\\": \\\"rest\\\"\\n  },\\n  \\\"paths\\\": {\\n    \\\"/api/upload\\\": {\\n      \\\"post\\\": {\\n        \\\"consumes\\\": [\\n          \\\"multipart/form-data\\\"\\n        ],\\n        \\\"operationId\\\": \\\"uploadUsingPOST\\\",\\n        \\\"parameters\\\": [\\n          {\\n            \\\"description\\\": \\\"file\\\",\\n            \\\"in\\\": \\\"formData\\\",\\n            \\\"name\\\": \\\"file\\\",\\n            \\\"required\\\": true,\\n            \\\"type\\\": \\\"file\\\"\\n          },\\n          {\\n            \\\"description\\\": \\\"taskId\\\",\\n            \\\"in\\\": \\\"query\\\",\\n            \\\"name\\\": \\\"taskId\\\",\\n            \\\"required\\\": true,\\n            \\\"type\\\": \\\"string\\\"\\n          }\\n        ],\\n        \\\"produces\\\": [\\n          \\\"*/*\\\"\\n        ],\\n        \\\"responses\\\": {\\n          \\\"200\\\": {\\n            \\\"description\\\": \\\"OK\\\",\\n            \\\"schema\\\": {\\n              \\\"$ref\\\": \\\"#/definitions/RestResponse«JSONObject»\\\"\\n            }\\n          },\\n          \\\"201\\\": {\\n            \\\"description\\\": \\\"Created\\\"\\n          },\\n          \\\"401\\\": {\\n            \\\"description\\\": \\\"Unauthorized\\\"\\n          },\\n          \\\"403\\\": {\\n            \\\"description\\\": \\\"Forbidden\\\"\\n          },\\n          \\\"404\\\": {\\n            \\\"description\\\": \\\"Not Found\\\"\\n          }\\n        },\\n        \\\"summary\\\": \\\"上传文件\\\",\\n        \\\"tags\\\": [\\n          \\\"api-controller\\\"\\n        ]\\n      }\\n    }\\n  },\\n  \\\"definitions\\\": {\\n    \\\"DocumentSnBatch\\\": {\\n      \\\"properties\\\": {\\n        \\\"createdBy\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"createdTime\\\": {\\n          \\\"format\\\": \\\"date-time\\\",\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"deletedFlag\\\": {\\n          \\\"format\\\": \\\"int32\\\",\\n          \\\"type\\\": \\\"integer\\\"\\n        },\\n        \\\"documentNo\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"id\\\": {\\n          \\\"format\\\": \\\"int64\\\",\\n          \\\"type\\\": \\\"integer\\\"\\n        },\\n        \\\"isComponentsInvolved\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"materialBatchNumber\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"sn\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"updatedBy\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"updatedTime\\\": {\\n          \\\"format\\\": \\\"date-time\\\",\\n          \\\"type\\\": \\\"string\\\"\\n        }\\n      },\\n      \\\"title\\\": \\\"DocumentSnBatch\\\",\\n      \\\"type\\\": \\\"object\\\"\\n    },\\n    \\\"DocumentSnBatchDto\\\": {\\n      \\\"properties\\\": {\\n        \\\"documentNo\\\": {\\n          \\\"description\\\": \\\"单据编号\\\",\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"id\\\": {\\n          \\\"description\\\": \\\"id\\\",\\n          \\\"format\\\": \\\"int64\\\",\\n          \\\"type\\\": \\\"integer\\\"\\n        },\\n        \\\"isComponentsInvolved\\\": {\\n          \\\"description\\\": \\\"是否涉及组件\\\",\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"materialBatchNumber\\\": {\\n          \\\"description\\\": \\\"材料批次号\\\",\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"myPage\\\": {\\n          \\\"$ref\\\": \\\"#/definitions/MyPage\\\"\\n        },\\n        \\\"sn\\\": {\\n          \\\"description\\\": \\\"单据编号\\\",\\n          \\\"type\\\": \\\"string\\\"\\n        }\\n      },\\n      \\\"title\\\": \\\"DocumentSnBatchDto\\\",\\n      \\\"type\\\": \\\"object\\\"\\n    },\\n    \\\"DocumentSnBatchVo\\\": {\\n      \\\"properties\\\": {\\n        \\\"createdBy\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"createdTime\\\": {\\n          \\\"format\\\": \\\"date-time\\\",\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"documentNo\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"id\\\": {\\n          \\\"format\\\": \\\"int64\\\",\\n          \\\"type\\\": \\\"integer\\\"\\n        },\\n        \\\"isComponentsInvolved\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"materialBatchNumber\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"sn\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"updatedBy\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"updatedTime\\\": {\\n          \\\"format\\\": \\\"date-time\\\",\\n          \\\"type\\\": \\\"string\\\"\\n        }\\n      },\\n      \\\"title\\\": \\\"DocumentSnBatchVo\\\",\\n      \\\"type\\\": \\\"object\\\"\\n    },\\n    \\\"File\\\": {\\n      \\\"properties\\\": {\\n        \\\"absolute\\\": {\\n          \\\"type\\\": \\\"boolean\\\"\\n        },\\n        \\\"absoluteFile\\\": {\\n          \\\"$ref\\\": \\\"#/definitions/File\\\"\\n        },\\n        \\\"absolutePath\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"canonicalFile\\\": {\\n          \\\"$ref\\\": \\\"#/definitions/File\\\"\\n        },\\n        \\\"canonicalPath\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"directory\\\": {\\n          \\\"type\\\": \\\"boolean\\\"\\n        },\\n        \\\"executable\\\": {\\n          \\\"type\\\": \\\"boolean\\\"\\n        },\\n        \\\"file\\\": {\\n          \\\"type\\\": \\\"boolean\\\"\\n        },\\n        \\\"freeSpace\\\": {\\n          \\\"format\\\": \\\"int64\\\",\\n          \\\"type\\\": \\\"integer\\\"\\n        },\\n        \\\"hidden\\\": {\\n          \\\"type\\\": \\\"boolean\\\"\\n        },\\n        \\\"lastModified\\\": {\\n          \\\"format\\\": \\\"int64\\\",\\n          \\\"type\\\": \\\"integer\\\"\\n        },\\n        \\\"name\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"parent\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"parentFile\\\": {\\n          \\\"$ref\\\": \\\"#/definitions/File\\\"\\n        },\\n        \\\"path\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"readable\\\": {\\n          \\\"type\\\": \\\"boolean\\\"\\n        },\\n        \\\"totalSpace\\\": {\\n          \\\"format\\\": \\\"int64\\\",\\n          \\\"type\\\": \\\"integer\\\"\\n        },\\n        \\\"usableSpace\\\": {\\n          \\\"format\\\": \\\"int64\\\",\\n          \\\"type\\\": \\\"integer\\\"\\n        },\\n        \\\"writable\\\": {\\n          \\\"type\\\": \\\"boolean\\\"\\n        }\\n      },\\n      \\\"title\\\": \\\"File\\\",\\n      \\\"type\\\": \\\"object\\\"\\n    },\\n    \\\"InputStream\\\": {\\n      \\\"properties\\\": {},\\n      \\\"title\\\": \\\"InputStream\\\",\\n      \\\"type\\\": \\\"object\\\"\\n    },\\n    \\\"JSONObject\\\": {\\n      \\\"properties\\\": {},\\n      \\\"title\\\": \\\"JSONObject\\\",\\n      \\\"type\\\": \\\"object\\\"\\n    },\\n    \\\"Map«string,object»\\\": {\\n      \\\"properties\\\": {},\\n      \\\"title\\\": \\\"Map«string,object»\\\",\\n      \\\"type\\\": \\\"object\\\"\\n    },\\n    \\\"MrbInfo\\\": {\\n      \\\"properties\\\": {\\n        \\\"actualComponentsNumber\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"applicationsNumber\\\": {\\n          \\\"format\\\": \\\"int32\\\",\\n          \\\"type\\\": \\\"integer\\\"\\n        },\\n        \\\"approvalStatus\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"approverOfReliabilityTechnologyDepartment\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"bpmApplicant\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"causeAnalysis\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"commentsReliabilityTechnicalDepartment\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"completionDate\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"createdBy\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"createdTime\\\": {\\n          \\\"format\\\": \\\"date-time\\\",\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"currentProcessingNode\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"currentProcessor\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"deletedFlag\\\": {\\n          \\\"format\\\": \\\"int32\\\",\\n          \\\"type\\\": \\\"integer\\\"\\n        },\\n        \\\"descriptionAbnormalProduction\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"documentDate\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"documentNo\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"id\\\": {\\n          \\\"format\\\": \\\"int64\\\",\\n          \\\"type\\\": \\\"integer\\\"\\n        },\\n        \\\"isComponentsInvolved\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"manufacturerBatchNumber\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"materialNumber\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"moduleMw\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"mrbClassify\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"mrbReportUrl\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"mrbReviewResolution\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"objectType\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"problemBrief\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"productionResults\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"responsibleManufacturer\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"responsiblePartyType\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"secondaryNav\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"sn\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"taskid\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"unit\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"updatedBy\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"updatedTime\\\": {\\n          \\\"format\\\": \\\"date-time\\\",\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"volumeBarrelNumber\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"warrantyUrl\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        }\\n      },\\n      \\\"title\\\": \\\"MrbInfo\\\",\\n      \\\"type\\\": \\\"object\\\"\\n    },\\n    \\\"MrbInfoDto\\\": {\\n      \\\"properties\\\": {\\n        \\\"actualComponentsNumber\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"applicationsNumber\\\": {\\n          \\\"format\\\": \\\"int32\\\",\\n          \\\"type\\\": \\\"integer\\\"\\n        },\\n        \\\"approvalStatus\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"approverOfReliabilityTechnologyDepartment\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"bpmApplicant\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"causeAnalysis\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"commentsReliabilityTechnicalDepartment\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"completionDate\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"currentProcessingNode\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"currentProcessor\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"descriptionAbnormalProduction\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"documentDate\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"documentNo\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"id\\\": {\\n          \\\"format\\\": \\\"int64\\\",\\n          \\\"type\\\": \\\"integer\\\"\\n        },\\n        \\\"isComponentsInvolved\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"manufacturerBatchNumber\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"materialNumber\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"moduleMw\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"mrbClassify\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"mrbReportUrl\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"mrbReviewResolution\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"myPage\\\": {\\n          \\\"$ref\\\": \\\"#/definitions/MyPage\\\"\\n        },\\n        \\\"objectType\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"problemBrief\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"productionResults\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"responsibleManufacturer\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"responsiblePartyType\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"secondaryNav\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"sn\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"taskid\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"unit\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"volumeBarrelNumber\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"warrantyUrl\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        }\\n      },\\n      \\\"title\\\": \\\"MrbInfoDto\\\",\\n      \\\"type\\\": \\\"object\\\"\\n    },\\n    \\\"MyPage\\\": {\\n      \\\"properties\\\": {\\n        \\\"pageNum\\\": {\\n          \\\"format\\\": \\\"int32\\\",\\n          \\\"type\\\": \\\"integer\\\"\\n        },\\n        \\\"pageSize\\\": {\\n          \\\"format\\\": \\\"int32\\\",\\n          \\\"type\\\": \\\"integer\\\"\\n        }\\n      },\\n      \\\"title\\\": \\\"MyPage\\\",\\n      \\\"type\\\": \\\"object\\\"\\n    },\\n    \\\"MyPageData«DocumentSnBatchVo»\\\": {\\n      \\\"properties\\\": {\\n        \\\"dataList\\\": {\\n          \\\"items\\\": {\\n            \\\"$ref\\\": \\\"#/definitions/DocumentSnBatchVo\\\"\\n          },\\n          \\\"type\\\": \\\"array\\\"\\n        },\\n        \\\"pageNum\\\": {\\n          \\\"format\\\": \\\"int32\\\",\\n          \\\"type\\\": \\\"integer\\\"\\n        },\\n        \\\"pageSize\\\": {\\n          \\\"format\\\": \\\"int32\\\",\\n          \\\"type\\\": \\\"integer\\\"\\n        },\\n        \\\"pages\\\": {\\n          \\\"format\\\": \\\"int32\\\",\\n          \\\"type\\\": \\\"integer\\\"\\n        },\\n        \\\"total\\\": {\\n          \\\"format\\\": \\\"int64\\\",\\n          \\\"type\\\": \\\"integer\\\"\\n        }\\n      },\\n      \\\"title\\\": \\\"MyPageData«DocumentSnBatchVo»\\\",\\n      \\\"type\\\": \\\"object\\\"\\n    },\\n    \\\"MyPageData«mrb+csp+mes信息VO»\\\": {\\n      \\\"properties\\\": {\\n        \\\"dataList\\\": {\\n          \\\"items\\\": {\\n            \\\"$ref\\\": \\\"#/definitions/mrb+csp+mes信息VO\\\"\\n          },\\n          \\\"type\\\": \\\"array\\\"\\n        },\\n        \\\"pageNum\\\": {\\n          \\\"format\\\": \\\"int32\\\",\\n          \\\"type\\\": \\\"integer\\\"\\n        },\\n        \\\"pageSize\\\": {\\n          \\\"format\\\": \\\"int32\\\",\\n          \\\"type\\\": \\\"integer\\\"\\n        },\\n        \\\"pages\\\": {\\n          \\\"format\\\": \\\"int32\\\",\\n          \\\"type\\\": \\\"integer\\\"\\n        },\\n        \\\"total\\\": {\\n          \\\"format\\\": \\\"int64\\\",\\n          \\\"type\\\": \\\"integer\\\"\\n        }\\n      },\\n      \\\"title\\\": \\\"MyPageData«mrb+csp+mes信息VO»\\\",\\n      \\\"type\\\": \\\"object\\\"\\n    },\\n    \\\"Resource\\\": {\\n      \\\"properties\\\": {\\n        \\\"description\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"file\\\": {\\n          \\\"$ref\\\": \\\"#/definitions/File\\\"\\n        },\\n        \\\"filename\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"inputStream\\\": {\\n          \\\"$ref\\\": \\\"#/definitions/InputStream\\\"\\n        },\\n        \\\"open\\\": {\\n          \\\"type\\\": \\\"boolean\\\"\\n        },\\n        \\\"readable\\\": {\\n          \\\"type\\\": \\\"boolean\\\"\\n        },\\n        \\\"uri\\\": {\\n          \\\"$ref\\\": \\\"#/definitions/URI\\\"\\n        },\\n        \\\"url\\\": {\\n          \\\"$ref\\\": \\\"#/definitions/URL\\\"\\n        }\\n      },\\n      \\\"title\\\": \\\"Resource\\\",\\n      \\\"type\\\": \\\"object\\\"\\n    },\\n    \\\"RestFileExport\\\": {\\n      \\\"properties\\\": {\\n        \\\"filePath\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"userInfo\\\": {\\n          \\\"$ref\\\": \\\"#/definitions/UserInfo\\\"\\n        }\\n      },\\n      \\\"title\\\": \\\"RestFileExport\\\",\\n      \\\"type\\\": \\\"object\\\"\\n    },\\n    \\\"RestResponse«DocumentSnBatch»\\\": {\\n      \\\"properties\\\": {\\n        \\\"code\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"data\\\": {\\n          \\\"$ref\\\": \\\"#/definitions/DocumentSnBatch\\\"\\n        },\\n        \\\"message\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"powerType\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"success\\\": {\\n          \\\"type\\\": \\\"boolean\\\"\\n        }\\n      },\\n      \\\"title\\\": \\\"RestResponse«DocumentSnBatch»\\\",\\n      \\\"type\\\": \\\"object\\\"\\n    },\\n    \\\"RestResponse«JSONObject»\\\": {\\n      \\\"properties\\\": {\\n        \\\"code\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"data\\\": {\\n          \\\"properties\\\": {},\\n          \\\"type\\\": \\\"object\\\"\\n        },\\n        \\\"message\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"powerType\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"success\\\": {\\n          \\\"type\\\": \\\"boolean\\\"\\n        }\\n      },\\n      \\\"title\\\": \\\"RestResponse«JSONObject»\\\",\\n      \\\"type\\\": \\\"object\\\"\\n    },\\n    \\\"RestResponse«MrbInfo»\\\": {\\n      \\\"properties\\\": {\\n        \\\"code\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"data\\\": {\\n          \\\"$ref\\\": \\\"#/definitions/MrbInfo\\\"\\n        },\\n        \\\"message\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"powerType\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"success\\\": {\\n          \\\"type\\\": \\\"boolean\\\"\\n        }\\n      },\\n      \\\"title\\\": \\\"RestResponse«MrbInfo»\\\",\\n      \\\"type\\\": \\\"object\\\"\\n    },\\n    \\\"RestResponse«MyPageData«DocumentSnBatchVo»»\\\": {\\n      \\\"properties\\\": {\\n        \\\"code\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"data\\\": {\\n          \\\"$ref\\\": \\\"#/definitions/MyPageData«DocumentSnBatchVo»\\\"\\n        },\\n        \\\"message\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"powerType\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"success\\\": {\\n          \\\"type\\\": \\\"boolean\\\"\\n        }\\n      },\\n      \\\"title\\\": \\\"RestResponse«MyPageData«DocumentSnBatchVo»»\\\",\\n      \\\"type\\\": \\\"object\\\"\\n    },\\n    \\\"RestResponse«MyPageData«mrb+csp+mes信息VO»»\\\": {\\n      \\\"properties\\\": {\\n        \\\"code\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"data\\\": {\\n          \\\"$ref\\\": \\\"#/definitions/MyPageData«mrb+csp+mes信息VO»\\\"\\n        },\\n        \\\"message\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"powerType\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"success\\\": {\\n          \\\"type\\\": \\\"boolean\\\"\\n        }\\n      },\\n      \\\"title\\\": \\\"RestResponse«MyPageData«mrb+csp+mes信息VO»»\\\",\\n      \\\"type\\\": \\\"object\\\"\\n    },\\n    \\\"RestResponse«boolean»\\\": {\\n      \\\"properties\\\": {\\n        \\\"code\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"data\\\": {\\n          \\\"type\\\": \\\"boolean\\\"\\n        },\\n        \\\"message\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"powerType\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"success\\\": {\\n          \\\"type\\\": \\\"boolean\\\"\\n        }\\n      },\\n      \\\"title\\\": \\\"RestResponse«boolean»\\\",\\n      \\\"type\\\": \\\"object\\\"\\n    },\\n    \\\"RestResponse«long»\\\": {\\n      \\\"properties\\\": {\\n        \\\"code\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"data\\\": {\\n          \\\"format\\\": \\\"int64\\\",\\n          \\\"type\\\": \\\"integer\\\"\\n        },\\n        \\\"message\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"powerType\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"success\\\": {\\n          \\\"type\\\": \\\"boolean\\\"\\n        }\\n      },\\n      \\\"title\\\": \\\"RestResponse«long»\\\",\\n      \\\"type\\\": \\\"object\\\"\\n    },\\n    \\\"URI\\\": {\\n      \\\"properties\\\": {\\n        \\\"absolute\\\": {\\n          \\\"type\\\": \\\"boolean\\\"\\n        },\\n        \\\"authority\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"fragment\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"host\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"opaque\\\": {\\n          \\\"type\\\": \\\"boolean\\\"\\n        },\\n        \\\"path\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"port\\\": {\\n          \\\"format\\\": \\\"int32\\\",\\n          \\\"type\\\": \\\"integer\\\"\\n        },\\n        \\\"query\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"rawAuthority\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"rawFragment\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"rawPath\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"rawQuery\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"rawSchemeSpecificPart\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"rawUserInfo\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"scheme\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"schemeSpecificPart\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"userInfo\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        }\\n      },\\n      \\\"title\\\": \\\"URI\\\",\\n      \\\"type\\\": \\\"object\\\"\\n    },\\n    \\\"URL\\\": {\\n      \\\"properties\\\": {\\n        \\\"authority\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"content\\\": {\\n          \\\"properties\\\": {},\\n          \\\"type\\\": \\\"object\\\"\\n        },\\n        \\\"defaultPort\\\": {\\n          \\\"format\\\": \\\"int32\\\",\\n          \\\"type\\\": \\\"integer\\\"\\n        },\\n        \\\"deserializedFields\\\": {\\n          \\\"$ref\\\": \\\"#/definitions/URLStreamHandler\\\"\\n        },\\n        \\\"file\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"host\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"path\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"port\\\": {\\n          \\\"format\\\": \\\"int32\\\",\\n          \\\"type\\\": \\\"integer\\\"\\n        },\\n        \\\"protocol\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"query\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"ref\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"serializedHashCode\\\": {\\n          \\\"format\\\": \\\"int32\\\",\\n          \\\"type\\\": \\\"integer\\\"\\n        },\\n        \\\"userInfo\\\": {\\n          \\\"type\\\": \\\"string\\\"\\n        }\\n      },\\n      \\\"title\\\": \\\"URL\\\",\\n      \\\"type\\\": \\\"object\\\"\\n    },\\n    \\\"URLStreamHandler\\\": {\\n      \\\"properties\\\": {},\\n      \\\"title\\\": \\\"URLStreamHandler\\\",\\n      \\\"type\\\": \\\"object\\\"\\n    },\\n    \\\"UserInfo\\\": {\\n      \\\"description\\\": \\\"用户信息\\\",\\n      \\\"properties\\\": {\\n        \\\"actualName\\\": {\\n          \\\"description\\\": \\\"用户姓名\\\",\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"appname\\\": {\\n          \\\"description\\\": \\\"客户端名称\\\",\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"email\\\": {\\n          \\\"description\\\": \\\"用户邮箱\\\",\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"factory\\\": {\\n          \\\"description\\\": \\\"厂区\\\",\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"group\\\": {\\n          \\\"description\\\": \\\"工作组\\\",\\n          \\\"items\\\": {\\n            \\\"$ref\\\": \\\"#/definitions/Map«string,object»\\\"\\n          },\\n          \\\"type\\\": \\\"array\\\"\\n        },\\n        \\\"isEnabled\\\": {\\n          \\\"description\\\": \\\"是否可用\\\",\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"isInternal\\\": {\\n          \\\"description\\\": \\\"是否内部员工\\\",\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"password\\\": {\\n          \\\"description\\\": \\\"密码\\\",\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"phone\\\": {\\n          \\\"description\\\": \\\"用户手机\\\",\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"remark\\\": {\\n          \\\"description\\\": \\\"备注\\\",\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"role\\\": {\\n          \\\"description\\\": \\\"角色\\\",\\n          \\\"items\\\": {\\n            \\\"$ref\\\": \\\"#/definitions/Map«string,object»\\\"\\n          },\\n          \\\"type\\\": \\\"array\\\"\\n        },\\n        \\\"sex\\\": {\\n          \\\"description\\\": \\\"用户性别\\\",\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"username\\\": {\\n          \\\"description\\\": \\\"用户账号\\\",\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"workshop\\\": {\\n          \\\"description\\\": \\\"车间\\\",\\n          \\\"items\\\": {\\n            \\\"$ref\\\": \\\"#/definitions/Map«string,object»\\\"\\n          },\\n          \\\"type\\\": \\\"array\\\"\\n        }\\n      },\\n      \\\"title\\\": \\\"UserInfo\\\",\\n      \\\"type\\\": \\\"object\\\"\\n    },\\n    \\\"mes信息VO\\\": {\\n      \\\"properties\\\": {\\n        \\\"defaultvalue\\\": {\\n          \\\"description\\\": \\\"厂家\\\",\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"materiallotno\\\": {\\n          \\\"description\\\": \\\"材料批号\\\",\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"propertyno\\\": {\\n          \\\"description\\\": \\\"厂家编号\\\",\\n          \\\"type\\\": \\\"string\\\"\\n        }\\n      },\\n      \\\"title\\\": \\\"mes信息VO\\\",\\n      \\\"type\\\": \\\"object\\\"\\n    },\\n    \\\"mrb+csp+mes信息VO\\\": {\\n      \\\"properties\\\": {\\n        \\\"actualComponentsNumber\\\": {\\n          \\\"description\\\": \\\"实际组件数量\\\",\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"applicationsNumber\\\": {\\n          \\\"description\\\": \\\"申请数量\\\",\\n          \\\"format\\\": \\\"int32\\\",\\n          \\\"type\\\": \\\"integer\\\"\\n        },\\n        \\\"id\\\": {\\n          \\\"format\\\": \\\"int64\\\",\\n          \\\"type\\\": \\\"integer\\\"\\n        },\\n        \\\"mesInfoList\\\": {\\n          \\\"description\\\": \\\"SN对应的mes信息\\\",\\n          \\\"items\\\": {\\n            \\\"$ref\\\": \\\"#/definitions/mes信息VO\\\"\\n          },\\n          \\\"type\\\": \\\"array\\\"\\n        },\\n        \\\"moduleMw\\\": {\\n          \\\"description\\\": \\\"组件兆瓦数\\\",\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"mrbClassify\\\": {\\n          \\\"description\\\": \\\"MRB分类;0-(可靠性、外观、功率、EL)\\\\n     1-(可靠性、外观、EL、尺寸 材料认证、产地认证)\\\\n     2-(电池片、玻璃、EVA、汇流条、焊带、型材、接线盒)\\\\n     3-(可靠性、外观、EL)\\\",\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"mrbReportUrl\\\": {\\n          \\\"description\\\": \\\"MRB报告地址\\\",\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"responsiblePartyType\\\": {\\n          \\\"description\\\": \\\"责任方;0-天合 1-供应商\\\",\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"sn\\\": {\\n          \\\"description\\\": \\\"SN(组建号)\\\",\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"unit\\\": {\\n          \\\"description\\\": \\\"单位\\\",\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"warrantyUrl\\\": {\\n          \\\"description\\\": \\\"质保单地址\\\",\\n          \\\"type\\\": \\\"string\\\"\\n        }\\n      },\\n      \\\"title\\\": \\\"mrb+csp+mes信息VO\\\",\\n      \\\"type\\\": \\\"object\\\"\\n    }\\n  },\\n  \\\"responses\\\": {\\n    \\\"200\\\": {\\n      \\\"description\\\": \\\"Successful API response.\\\",\\n      \\\"headers\\\": {\\n        \\\"X-Ratelimit-Limit\\\": {\\n          \\\"default\\\": \\\"name=eco-ratelimit10\\\",\\n          \\\"description\\\": \\\"\\\\\\\"name=eco-ratelimit,10\\\\\\\": name is the rate limit policy name and 10 means the total number of the request in current limit window\\\",\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"X-Ratelimit-Remaining\\\": {\\n          \\\"default\\\": \\\"name=eco-ratelimit,5\\\",\\n          \\\"description\\\": \\\"\\\\\\\"name=eco-ratelimit,0\\\\\\\": name is the rate limit policy name and 5 means the remaining number of request is 5 in current limit window.\\\",\\n          \\\"type\\\": \\\"string\\\"\\n        }\\n      }\\n    },\\n    \\\"429\\\": {\\n      \\\"description\\\": \\\"If the request overhead the rate limit number, the API will return this status code.\\\",\\n      \\\"headers\\\": {\\n        \\\"X-Ratelimit-Limit\\\": {\\n          \\\"default\\\": \\\"name=eco-ratelimit10\\\",\\n          \\\"description\\\": \\\"\\\\\\\"name=eco-ratelimit,10\\\\\\\": name is the rate limit policy name and 10 means the total number of the request in current limit window\\\",\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"X-Ratelimit-Remaining\\\": {\\n          \\\"default\\\": \\\"name=eco-ratelimit,5\\\",\\n          \\\"description\\\": \\\"\\\\\\\"name=eco-ratelimit,0\\\\\\\": name is the rate limit policy name and 5 means the remaining number of request is 5 in current limit window.\\\",\\n          \\\"type\\\": \\\"string\\\"\\n        },\\n        \\\"X-Ratelimit-Reset\\\": {\\n          \\\"default\\\": \\\"name=eco-ratelimit,52\\\",\\n          \\\"description\\\": \\\"\\\\\\\"name=eco-ratelimit,52\\\\\\\": name is the rate limit policy name and 52 means that 52 seconds remaining to next limit window.\\\",\\n          \\\"type\\\": \\\"string\\\"\\n        }\\n      }\\n    }\\n  },\\n  \\\"tags\\\": [\\n    {\\n      \\\"description\\\": \\\"Mrb Info Controller\\\",\\n      \\\"name\\\": \\\"MRB保存BPM信息\\\"\\n    },\\n    {\\n      \\\"description\\\": \\\"Api Controller\\\",\\n      \\\"name\\\": \\\"api-controller\\\"\\n    },\\n    {\\n      \\\"description\\\": \\\"File Export Controller\\\",\\n      \\\"name\\\": \\\"file-export-controller\\\"\\n    },\\n    {\\n      \\\"description\\\": \\\"Document Sn Batch Controller\\\",\\n      \\\"name\\\": \\\"单号关联\\\"\\n    }\\n  ],\\n  \\\"security\\\": [\\n    {\\n      \\\"TSL-ClientID\\\": [],\\n      \\\"TSL-ClientSecret\\\": []\\n    }\\n  ]\\n}",
