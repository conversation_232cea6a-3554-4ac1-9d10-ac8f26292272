package com.trinasolar.integration.controller.devops.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.ToString;


@Schema(description = "DevOps产品 Response 的 VO")
@Data
@ToString(callSuper = true)
public class DevOpsProjectRespVO {
    @Schema(description = "devops项目（产品）ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
    private String projectId;

    @Schema(description = "devops项目（产品）名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
    @NotEmpty(message = "项目名称不能为空")
    private String projectName;

    private String isNew;

    //原始数据
    private String originData;
}
