package com.trinasolar.integration.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 共享组件管理表
 *
 * <AUTHOR>
 */
@Data
@TableName("tasp_share_component")
public class ShareComponentDO implements Serializable {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 组件名称
     */
    @TableField(value = "component_name")
    private String componentName;

    /**
     * 版本号
     */
    @TableField(value = "version")
    private String version;

    /**
     * 组件描述
     */
    @TableField(value = "description")
    private String description;

    /**
     * 文件id
     */
    @TableField(value = "file_id")
    private Long fileId;

    /**
     * 文件名称
     */
    @TableField(value = "file_name")
    private String fileName;

    /**
     * 文件url
     */
    @TableField(value = "file_url")
    private String fileUrl;

    /**
     * logo的url
     */
    @TableField(value = "logo_url")
    private String logoUrl;


    /**
     * 状态（0正常 1停用）
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 创建者
     */
    @TableField(value = "creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(value = "updater")
    private String updater;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 是否删除：0：不删除，1：删除
     */
    @TableField(value = "deleted")
    private Integer deleted;

    /**
     *
     * 文件类型
     */
    @TableField(value = "file_type")
    private String fileType;
}
