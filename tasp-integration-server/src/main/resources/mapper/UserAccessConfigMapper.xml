<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trinasolar.integration.dao.UserAccessConfigMapper">
    <insert id="insertBatch">
        INSERT INTO tasp_user_access_config (user_id, access_type, logo_url, name, url, sort, creator, create_time, updater, update_time) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.userId}, #{item.accessType}, #{item.logoUrl}, #{item.name}, #{item.url}, #{item.sort}, #{item.creator}, #{item.createTime}, #{item.updater}, #{item.updateTime})
        </foreach>
    </insert>
    <select id="noticePage" resultType="com.trinasolar.integration.controller.personal.NoticeBO">
        SELECT id as id, name as content, create_time as create_time FROM tasp_user_access_config WHERE access_type = 3
    </select>
    <delete id="deleteByUserId">
        DELETE FROM tasp_user_access_config WHERE user_id = #{userId}
    </delete>
    <select id="selectByUserId" resultType="com.trinasolar.integration.controller.personal.UserAccessConfigBO">
        SELECT * FROM tasp_user_access_config WHERE access_type = 3 AND user_id = #{userId} order by sort
    </select>
    <select id="selectDefaultConfig"
            resultType="com.trinasolar.integration.controller.personal.UserAccessConfigBO">
        SELECT * FROM tasp_user_access_config WHERE access_type = #{accessType}
    </select>
</mapper>
