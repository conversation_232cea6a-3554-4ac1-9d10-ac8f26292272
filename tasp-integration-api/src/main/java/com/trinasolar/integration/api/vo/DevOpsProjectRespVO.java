package com.trinasolar.integration.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.ToString;


@Schema(description = "DevOps产品 Response 的 VO")
@Data
@ToString(callSuper = true)
public class DevOpsProjectRespVO {
    @Schema(description = "项目ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
    private String projectId;

    @Schema(description = "项目名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
    @NotEmpty(message = "项目名称不能为空")
    private String projectName;

    private String isNew;

    //原始数据
    private String originData;
}
