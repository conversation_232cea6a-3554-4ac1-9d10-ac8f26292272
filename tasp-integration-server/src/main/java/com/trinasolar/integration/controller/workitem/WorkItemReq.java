package com.trinasolar.integration.controller.workitem;

import com.trinasolar.integration.controller.personal.PageReq;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 工作项
 */
@Data
public class WorkItemReq extends PageReq implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 标题
     */
    private String title;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     *  类型
     */
    private String type;

}
