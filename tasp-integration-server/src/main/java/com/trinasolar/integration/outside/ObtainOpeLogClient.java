package com.trinasolar.integration.outside;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.trinasolar.integration.api.dto.OpeLogQueryBuilder;
import com.trinasolar.integration.api.dto.OpeLogResponseResult;
import com.trinasolar.integration.api.dto.OplogDataDTO;
import com.trinasolar.integration.util.HttpClientUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 日志易API请求客户端
 */
@Component
@Slf4j
public class ObtainOpeLogClient {

    // 项目开始时间
    private static final long CLIENT_START_DATE = 1735664400000L;

    @Value("${searchOpeLog.queryUrl}")
    private String opeLogQueryUrl;

    /**
     * 调用操作日志平台-应用系统
     * @return
     */
    public List<OplogDataDTO> searchAppOpeLog(OplogDataDTO queryDTO) {
        try{
            long currentTime = System.currentTimeMillis();
            String urlParams = String.format("&clientStartDate=%d&clientEndDate=%d&", CLIENT_START_DATE, currentTime);
            String url = opeLogQueryUrl + "?size=5&from=0" + urlParams;
            log.info("调用应用系统操作日志平台提交接口-入参：{},URL:{}", JSONObject.toJSON(queryDTO), url);
            Map<String,Object> param = new HashMap<>();
            builderSearchAppParams(param, queryDTO);
            Map<String,String> header = new HashMap<>();
            header.put("Content-Type", "application/json");
            String resultStr = HttpClientUtil.doPostJson(url, param, header);
            log.info("dto:{}", JSONObject.toJSON(param));
            log.info("调用应用系统操作日志平台接口返回结果：{}", JSONObject.toJSON(resultStr));
            if (StringUtils.isNotEmpty(resultStr)) {
                OpeLogResponseResult resultDTO = JSONObject.parseObject(resultStr, new TypeReference<OpeLogResponseResult>() {});
                if (Objects.nonNull(resultDTO.getHits()) && CollectionUtil.isNotEmpty(resultDTO.getHits().getHits())) {
                    return resultDTO.getHits().getHits().stream().map(hitItem -> hitItem.get_source()).filter(Objects::nonNull).toList();
                }
            }
            return null;
        }catch (Exception e) {
            log.error("调用应用系统操作日志平台接口报错:", e);
            return null;
        }
    }

    private void builderSearchAppParams(Map<String, Object> param, OplogDataDTO queryDTO) {
        OpeLogQueryBuilder queryBuilder = new OpeLogQueryBuilder();
        // 添加must条件
        queryBuilder.getQuery().getBool().addMatchPhrase("threadName", queryDTO.getOperatorName());
        queryBuilder.getQuery().getBool().addMatchPhrase("appName", "tasp-kepler");
        queryBuilder.getQuery().getBool().addMatchPhrase("serviceName", "harmonycloud-kepler-cloud-biz");
        queryBuilder.getQuery().getBool().addQueryString(queryDTO.getContent());
        queryBuilder.getQuery().getBool().addRange("dtTime", CLIENT_START_DATE, System.currentTimeMillis());
        // 添加排序条件
        queryBuilder.addSort("dtTime", "desc");
        // 添加折叠条件
        queryBuilder.addCollapse("requestParam.keyword");
        // 添加聚合条件

        param.put("query", queryBuilder.getQuery());
        param.put("sort", queryBuilder.getSort());
        param.put("collapse", queryBuilder.getCollapse());
    }

    private void builderSearchDocParams(Map<String, Object> param, OplogDataDTO queryDTO) {
        OpeLogQueryBuilder queryBuilder = new OpeLogQueryBuilder();
        // 添加must条件
        queryBuilder.getQuery().getBool().addMatchPhrase("threadName", "Yang_Gang TS/TPD(杨刚)");
       // queryBuilder.getQuery().getBool().addMatchPhrase("className", "/apps/");
        queryBuilder.getQuery().getBool().addMatchPhrase("appName", "tasp-kepler");
        queryBuilder.getQuery().getBool().addMatchPhrase("serviceName", "harmonycloud-kepler-cloud-biz");
        queryBuilder.getQuery().getBool().addQueryString("应用系统详情");
        queryBuilder.getQuery().getBool().addRange("dtTime", CLIENT_START_DATE, System.currentTimeMillis());
        // 添加排序条件
        queryBuilder.addSort("dtTime", "desc");
        // 添加折叠条件
        queryBuilder.addCollapse("requestParam.keyword");
        // 添加聚合条件
        queryBuilder.addAggregations("requestParam.keyword");
        param.put("query", queryBuilder.getQuery());
        param.put("sort", queryBuilder.getSort());
        param.put("collapse", queryBuilder.getCollapse());
        param.put("aggs", queryBuilder.getAggregations());
    }

}
