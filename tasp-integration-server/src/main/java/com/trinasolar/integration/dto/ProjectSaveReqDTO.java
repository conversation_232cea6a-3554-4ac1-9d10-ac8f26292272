package com.trinasolar.integration.dto;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;


@Data
public class ProjectSaveReqDTO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 项目名称
     */
    @NotEmpty(message = "项目名称不能为空")
    private String name;

    @NotEmpty(message = "项目编码不能为空")
    private String code;

    /**
     * 项目描述
     */
    private String description;

    /**
     * 负责人
     */
    private String masterName;

    @NotNull(message = "状态（0正常 1停用）不能为空")
    private Integer status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 团队名称，用于K8S namespace和 Git group
     */
    @NotNull(message = "团队名称不能为空")
    private String namespace;
}
