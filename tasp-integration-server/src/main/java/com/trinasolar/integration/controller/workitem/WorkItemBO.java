package com.trinasolar.integration.controller.workitem;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 工作项
 */
@Data
public class WorkItemBO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 标签
     */
    private String tag;

    /**
     * 标题
     */
    private String title;

    /**
     * 提交时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 状态
     */
    private String status;

    /**
     * 应用系统
     */
    private String appSystem;

    /**
     * 应用程序
     */
    private String appProgram;


    /**
     * 停留时间
     */
    private String dwellTime;

    /**
     * 类型
     */
    private String type;


    /**
     * 发起人
     */
    private String creator;
}
