package com.trinasolar.integration.controller.devops.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;
import org.apache.poi.ss.formula.functions.T;

/**
 * <AUTHOR>
 * DevOps产品修改
 */
@Data
@ToString(callSuper = true)
public class DevOpsProjectResp<T> {

    private Integer status;

    private T data;

    private Integer code;

    private String traceId;
}
