package com.trinasolar.integration.api.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */

@Getter
public enum CheckStatusEnum {
    DEFAULT(0, "默认"),
    WAITING(1, "等待"),
    SCANNING(2, "扫描中"),
    FINISHED(3, "结束"),
    ERROR(4, "扫描出错"),
    STOPPED(5, "停止"),
    TIMEOUT(6, "超时");

    @EnumValue
    private final int code;
    private final String desc;

    CheckStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static Boolean isFinished(Integer code) {
        return code > FINISHED.getCode() || code == FINISHED.getCode();
    }

    public static List<Integer> getNotFinishedCode() {
        return List.of(DEFAULT.getCode(), SCANNING.getCode(), WAITING.getCode());
    }

    public static CheckStatusEnum getByCode(Integer checkStatus) {
        CheckStatusEnum checkStatusEnum = Arrays.stream(CheckStatusEnum.values()).filter(e -> e.getCode() == checkStatus).findFirst().orElse(FINISHED);
        return checkStatusEnum;
    }

    public int getCode() {
        return code;
    }

    @JsonValue
    public String getDesc() {
        return desc;
    }
}