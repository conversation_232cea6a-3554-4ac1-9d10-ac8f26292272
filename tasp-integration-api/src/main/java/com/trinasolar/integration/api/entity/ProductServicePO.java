package com.trinasolar.integration.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 产品信息持久化对象
 *
 * <AUTHOR>
 */
@Data
@TableName("product_service")
//@EqualsAndHashCode(callSuper = false)
public class ProductServicePO {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 产品id
     */
    private Long productId;

    /**
     * 产品名称
     */
    private String name;

    /**
     * 产品描述
     */
    private String description;

    /**
     * 产品页面跳转URL
     */
    private String url;

    /**
     * 产品logo URL
     */
    private String logoUrl;


    /**
     * 归属类型 用于前端跳转
     */
    private String type;

    /**
     * 供应商
     */
    private String supplier;

    /**
     * 产品分类
     */
    private String category;

    /**
     * 产品版本
     */
    private String version;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 共享组件id
     */
    private Long componentId;

    /**
     * 创建人
     */
    private String creator;


    /**
     * 更新人
     */
    private String updater;

    /**
     * 文件类型
     */
    private String fileType;
}
