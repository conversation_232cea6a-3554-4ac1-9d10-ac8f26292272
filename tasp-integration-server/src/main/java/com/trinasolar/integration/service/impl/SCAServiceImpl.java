package com.trinasolar.integration.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trinasolar.integration.api.SCAProvider;
import com.trinasolar.integration.api.ScaApiProvider;
import com.trinasolar.integration.api.UpmsProvider;
import com.trinasolar.integration.api.common.enums.HttpMethod;
import com.trinasolar.integration.api.dto.*;
import com.trinasolar.integration.api.entity.*;
import com.trinasolar.integration.api.enums.CheckStatusEnum;
import com.trinasolar.integration.api.service.OpenSourceComponentBaselineService;
import com.trinasolar.integration.dao.AppProgramComponentMapper;
import com.trinasolar.integration.dao.AppSystemInitializeMapper;
import com.trinasolar.integration.dao.ApplicationProgramMapper;
import com.trinasolar.integration.execption.SCAException;
import com.trinasolar.integration.sca.entity.SCAScanTaskRecords;
import com.trinasolar.integration.service.AppProgramComponentService;
import com.trinasolar.integration.service.OpenSourceComptBaselineRegisterService;
import com.trinasolar.integration.service.SCAScanTaskRecordsService;
import com.trinasolar.tasc.framework.common.pojo.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.maven.artifact.versioning.ComparableVersion;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 参数维护工具类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class SCAServiceImpl extends SCATokenService {

    @Autowired
    private SCAProvider scaProvider;

    @Autowired
    private ScaApiProvider scaApiProvider;

    @Autowired
    private OpenSourceComponentBaselineService openSourceComponentBaselineService;

    @Autowired
    private UpmsProvider UpmsProvider;

    @Value("${trinasolar.git.token}")
    protected String gitToken;

    @Autowired
    private AppProgramComponentService programCmptService;

    @Autowired
    AppProgramComponentMapper appProgramComponentMapper;

    @Autowired
    SCAScanTaskRecordsService SCAScanTaskRecordsService;

    @Autowired
    AppSystemInitializeMapper appSystemInitializeMapper;
    @Autowired
    private ApplicationProgramMapper applicationProgramMapper;

    @Override
    public ScaResponseDTO<ScaDataDTO> getModules(Long projectId) {
        Map<String, Object> map = new HashMap<>();
        map.put("project_id", projectId);
        map.put("scan_status", -1);
        map.put("code_type_num", -1);
        map.put("safe_level", -1);
        map.put("num", 1000);
        map.put("page", 1);
        map.put("order_by_created_time", true);
        map.put("order_by_latest_scan_time", true);
        Map<String, String> headerMap = generateSignatureParams(HttpMethod.GET.getValue(), null, map);
        return scaProvider.getModules(headerMap, projectId, 1, 1000, -1, -1, -1, true, true);
    }

    @Override
    public SCAPage<ScaTaskDetailDTO> getModulesScanHistory(Long appId, Integer pageNo, Integer pageSize) {
        String id = getProjectId(appId);
        String componentId = getComponentId(appId);
        // 2. 处理分页参数默认值
        if (pageNo == null || pageNo <= 0) {
            pageNo = 1;
        }
        if (pageSize == null || pageSize <= 0) {
            pageSize = 10;
        }

        ScaResponseDTO<ScaDataDTO> modules = getModules(Long.valueOf(id));
        if (modules.isError()) {
            log.error("获取模块异常");
            throw new SCAException(modules.getMessage());
        }
        List<ScaItemDTO> items = modules.getData().getItems();
        Optional<ScaItemDTO> optionalScaItemDTO = items.stream().filter(item -> componentId.equals(String.valueOf(item.getId()))).findFirst();
        if (optionalScaItemDTO.isEmpty()) {
            SCAPage<ScaTaskDetailDTO> result = new SCAPage<>();
            result.setTotal(0);
            result.setPageSize(pageSize);
            result.setPageNo(pageNo);
            result.setRecords(new ArrayList<>());
            return result;
        }
        // 一个模块只有一个代码地址
        ScaItemDTO scaItemDTO = optionalScaItemDTO.get();
        List<ScaItemDTO.Task> tasks = scaItemDTO.getTasks();
        if (CollectionUtils.isEmpty(tasks)) {
            SCAPage<ScaTaskDetailDTO> result = new SCAPage<>();
            result.setTotal(0);
            result.setPageSize(pageSize);
            result.setPageNo(pageNo);
            result.setRecords(new ArrayList<>());
            return result;
        }

        // 1. 按taskId倒序排序
        tasks.sort(Comparator.comparing(ScaItemDTO.Task::getId).reversed());


        // 3. 计算分页起始位置和结束位置
        int startIndex = (pageNo - 1) * pageSize;
        int endIndex = Math.min(startIndex + pageSize, tasks.size());

        // 4. 截取分页后的任务列表
        List<ScaItemDTO.Task> paginatedTasks = tasks.subList(startIndex, endIndex);
        List<ScaTaskDetailDTO> scaTaskDetail = new ArrayList<>();
        // 5. 处理分页后的任务
        paginatedTasks.forEach(t -> {
            Map<String, String> stringStringMap = generateSignatureParams(HttpMethod.GET.getValue(), null, null);
            ScaResponseDTO<ScaTaskDetailDTO> taskDetails = scaProvider.getTaskDetails(stringStringMap, t.getId());
            if (taskDetails.isSuccess()) {
                scaTaskDetail.add(taskDetails.getData());
            }
        });
        // 6. 构造分页结果
        SCAPage<ScaTaskDetailDTO> result = new SCAPage<>();
        result.setTotal(tasks.size());
        result.setPageSize(pageSize);
        result.setPageNo(pageNo);
        result.setRecords(scaTaskDetail);
        result.setPages((int) Math.ceil((double) tasks.size() / pageSize));
        return result;
    }

    @Override
    public SCAPage<ComponentInfo> getCompsByTaskId(Long taskId, Integer pageNo, Integer pageSize, String pattern, Integer source) {
        Map<String, Object> map = new HashMap<>();
        map.put("pattern", pattern);
        map.put("source", source);
        map.put("page", pageNo);
        map.put("num", pageSize);
        map.put("dep_type", -1);
        //SCAPage<ComponentInfo>
        ScaResponseDTO<SCAPage<ComponentInfo>> comps = scaProvider.getCompsByTaskId(generateSignatureParams(HttpMethod.GET.getValue(), null, map), taskId, pageNo, pageSize, pattern, source, -1);
        List<OpenSourceComponentBaseline> openSourceComponentBaselines = openSourceComponentBaselineService.list();
        Map<String, String> baselinesMap = openSourceComponentBaselines.stream().filter(e -> StringUtils.isNotEmpty(e.getName()) && StringUtils.isNotEmpty(e.getVersion()))
                .collect(Collectors.toMap(OpenSourceComponentBaseline::getName, OpenSourceComponentBaseline::getVersion));
        SCAPage<ComponentInfo> data = comps.getData();
        List<ComponentInfo> items = comps.getData().getRecords();
        items.forEach(item -> {
            String scanVersion = item.getVersion();
            String name = item.getName();
            String baselinesVersion = baselinesMap.get(name);
            if (StringUtils.isEmpty(baselinesVersion)) {
                item.setBaseLineStatus("暂未维护");
                return;
            }
            ComparableVersion v1 = new ComparableVersion(baselinesVersion);
            ComparableVersion v2 = new ComparableVersion(scanVersion);
            int result = v1.compareTo(v2);
            if (result < 0) {
                item.setBaseLineStatus("不符合基线");
            } else {
                item.setBaseLineStatus("符合基线");
            }
        });
        return data;
    }

    @Override
    public ScanTaskResultDTO getCompsByTaskId(Long taskId) {
        ScaResponseDTO<ScanTaskResultDTO> scaResponseDTO = scaProvider.getResultByTaskId(generateSignatureParams(HttpMethod.GET.getValue(), null, null), taskId);
        return scaResponseDTO.getData();
    }


    /**
     * 创建SCA项目，应用系统对应SCA项目
     *
     * @param systemCnName 应用系统中文名
     * @return sca项目ID
     */
    @Override
    public Integer createProjects(String systemCnName, List<UserApp> userApps) {
        List<Integer> scaUserID = getScaUserID(userApps);
        JSONObject body = new JSONObject();
        body.put("get_or_create", 1);//如果项目名称存在则获取其id"(选填)  // 0 否, 1 是
        body.put("members", scaUserID);//项目成员(选填)
        body.put("name", systemCnName);//项目名称(必填)
        log.info("create sca projects body:{}", JSON.toJSONString(body));
        ScaResponseDTO<Object> projects = scaProvider.createProjects(generateSignatureParams(HttpMethod.POST.getValue(), body, null), body);
        log.info("create sca projects result:{}", JSON.toJSONString(projects));
        if (projects.isSuccess()) {
            Object data = projects.getData();
            JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(data));
            Long projectId = jsonObject.getLong("id");
            //如果项目已存在，且没有成员，则需要添加成员
            if (Integer.valueOf(41201).equals(projects.getCode()) && "新建项目失败，项目已存在".equals(projects.getMessage())) {
                log.info("sca项目已存在，且没有成员，则需要添加成员");
                ScaResponseDTO<ScaProjectDTO> projectDetail = scaProvider.getProjectDetail(generateSignatureParams(HttpMethod.GET.getValue(), null, null), projectId);
                log.info("get projectDetail result:{}", JSON.toJSONString(projectDetail));
                if (projectDetail.isSuccess() && CollUtil.isEmpty(projectDetail.getData().getMembers())) {
                    updateProjects(projectId, systemCnName, userApps);
                }
            }
            if (!projects.getOk()) {
                throw new SCAException("创建SCA项目失败: " + projects.getMessage());
            }
            return projectId.intValue();
        }
        return null;
    }

    /**
     * 删除SCA项目
     *
     * @param scaProjectId SCA项目ID
     * @return 是否删除成功
     */
    @Override
    public boolean updateProjects(Long scaProjectId, String systemCnName, List<UserApp> userApps) {
        List<Integer> scaUserID = getScaUserID(userApps);
        JSONObject body = new JSONObject();
        body.put("members", scaUserID);//项目成员(选填)
        body.put("name", systemCnName);//项目名称(必填)
        body.put("id", scaProjectId);//项目名称(必填)
        ScaResponseDTO<Object> projects = scaApiProvider.updateProjects(generateSignatureParams(HttpMethod.PUT.getValue(), body, null), body, scaProjectId);
        log.info("updte projects result:{}", JSON.toJSONString(projects));
        return projects.isSuccess();
    }

    /**
     * 删除SCA项目
     *
     * @param scaProjectId SCA项目ID
     * @return 是否删除成功
     */
    @Override
    public ScaResponseDTO<ScaProjectDTO> getProjectDtail(Long scaProjectId) {
        JSONObject body = new JSONObject();
        body.put("id", scaProjectId);//项目名称(必填)
        ScaResponseDTO<ScaProjectDTO> projects = scaProvider.getProjectDetail(generateSignatureParams(HttpMethod.GET.getValue(), body, null), scaProjectId);
        return projects;
    }

    /**
     * 删除SCA项目
     *
     * @param sysId 应用程序id
     * @return 是否删除成功
     */
    @Override
    public boolean deleteProjects(Long sysId) {
        LambdaQueryWrapper<AppSystemInitialize> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppSystemInitialize::getAppId, sysId);
        queryWrapper.eq(AppSystemInitialize::getRelationType, "sca");
        AppSystemInitialize appSystemInitialize = appSystemInitializeMapper.selectOne(queryWrapper);
        if (appSystemInitialize == null) {
            return true;
        }
        JSONObject body = new JSONObject();
        body.put("id", appSystemInitialize.getRelationId());
        ScaResponseDTO<Object> projects = scaProvider.deleteProjects(generateSignatureParams(HttpMethod.DELETE.getValue(), body, null), body);
        log.info("createProjects result:{}", JSON.toJSONString(projects));
        return projects.isSuccess();
    }

    @Override
    public ScanTaskDTO scan(Long appId) {
        String componentId = getComponentId(appId);
        JSONObject body = new JSONObject();
        body.put("module_id", Long.valueOf(componentId));
        ScaResponseDTO<ScanTaskDTO> scan = scaProvider.scan(generateSignatureParams(HttpMethod.POST.getValue(), body, null), body);
        if (scan.isError()) {
            throw new SCAException("扫描任务新建失败！因为：" + scan.getError());
        }
        ScanTaskDTO data = scan.getData();
        Long taskId = data.getTaskId();
        SCAScanTaskRecords SCAScanTaskRecords = new SCAScanTaskRecords();
        SCAScanTaskRecords.setTaskId(taskId);
        SCAScanTaskRecords.setAppId(appId);
        SCAScanTaskRecords.setCheckStatus(CheckStatusEnum.DEFAULT);
        SCAScanTaskRecordsService.save(SCAScanTaskRecords);
        // 开始记录
        return scan.getData();
    }

    @Override
    public ScanTaskProgressDTO scanProgress(Long taskId) {
        ScaResponseDTO<ScanTaskProgressDTO> scanTaskProgressDTOScaResponseDTO = scaProvider.scanProgress(generateSignatureParams(HttpMethod.GET.getValue(), null, null), taskId);
        if (scanTaskProgressDTOScaResponseDTO.isError()) {
            log.error("scanTaskProgressDTOScaResponseDTO is error{}", scanTaskProgressDTOScaResponseDTO.getError());
            throw new SCAException("获取扫描状态失败!");
        }
        return scanTaskProgressDTOScaResponseDTO.getData();
    }

    private String getComponentId(Long id) {
        LambdaQueryWrapper<AppProgramComponent> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppProgramComponent::getProgramId, id);
        queryWrapper.last("limit 1");
        AppProgramComponent appProgramComponent = programCmptService.getOne(queryWrapper);
        if (appProgramComponent == null) {
            throw new SCAException("未获取到执行的扫描模块配置：应用程序:" + id + ",请优先同步应用系统到SCA!");
        }
        return appProgramComponent.getComponentId();
    }

    private String getProjectId(Long id) {
        LambdaQueryWrapper<AppProgramComponent> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppProgramComponent::getProgramId, id);
        queryWrapper.last("LIMIT 1");
        AppProgramComponent appProgramComponent = programCmptService.getOne(queryWrapper);
        if (appProgramComponent == null) {
            throw new SCAException("未获取到执行的扫描模块配置：应用程序:" + id + ",请优先同步应用系统到SCA!");
        }
        return appProgramComponent.getProjectId();
    }

    @Override
    public Integer createModel(ApplicationProgram program, Integer projectId) {
        if (StrUtil.isEmpty(program.getGitlabRepoUrl())) {
            return null;
        }
        ScaModelCreateReqDTO scaModelCreateReqDTO = new ScaModelCreateReqDTO();
        scaModelCreateReqDTO.setName(program.getProgramNameCn());
        scaModelCreateReqDTO.setProjectId(projectId);
        scaModelCreateReqDTO.setPath(program.getGitlabRepoUrl());
        //  scaModelCreateReqDTO.setUsername("<EMAIL>");
        //  scaModelCreateReqDTO.setPasswd("bME%489x");
        scaModelCreateReqDTO.setUsername("root");
        scaModelCreateReqDTO.setPasswd(gitToken);
        scaModelCreateReqDTO.setBranch("master");
        scaModelCreateReqDTO.setAutoLang(1);
        JSONObject body = JSON.parseObject(JSON.toJSONString(scaModelCreateReqDTO));
        ScaResponseDTO<Object> modelResp = scaProvider.createModel(generateSignatureParams(HttpMethod.POST.getValue(), body, null), body);
        log.info("createModel result:{}", JSON.toJSONString(modelResp));
        if (modelResp.isSuccess()) {
            Object data = modelResp.getData();
            JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(data));
            return jsonObject.getInteger("id");
        } else if (Integer.valueOf(42400).equals(modelResp.getCode()) && modelResp.getMessage().contains("项目下存在同名的模块")) {
            ScaResponseDTO<ScaDataDTO> modules = getModules(Long.valueOf(projectId));
            ScaItemDTO scaItemDTO = modules.getData().getItems().stream()
                    .filter(e -> e.getName().equals(program.getProgramNameCn())).findFirst().get();
            return scaItemDTO.getId();
        } else {
            log.error("createModel error:{}", JSON.toJSONString(modelResp));
            return null;
        }
    }

    @Override
    public boolean deleteModel(Long programId) {
        LambdaQueryWrapper<AppProgramComponent> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppProgramComponent::getProgramId, programId);
        AppProgramComponent appProgramComponent = appProgramComponentMapper.selectOne(queryWrapper);
        if (appProgramComponent == null) {
            return true;
        }
        JSONObject body = new JSONObject();
        body.put("id", appProgramComponent.getProgramId());
        ScaResponseDTO<Object> projects = scaProvider.deleteModel(generateSignatureParams(HttpMethod.DELETE.getValue(), body, null), body);
        log.info("deleteModel result:{}", JSON.toJSONString(projects));
        return projects.isSuccess();
    }

    public List<Integer> getScaUserID(List<UserApp> userApps) {
        if (CollUtil.isEmpty(userApps)) {
            return null;
        }
        List<Long> userIds = userApps.stream().map(UserApp::getUserId).collect(Collectors.toList());
        R<List<UserDTO>> userDTOS = UpmsProvider.getByIds(userIds);
        List<String> userNames = userDTOS.getData().stream().map(UserDTO::getUserRealname).collect(Collectors.toList());
        ScaResponseDTO<SCAPage<ScaUserRespDTO>> responseDTO = scaProvider.getAllUser(generateSignatureParams(HttpMethod.GET.getValue(), null, null));
        if (responseDTO.isSuccess()) {
            return responseDTO.getData().getRecords().stream().filter(e -> userNames.contains(e.getName())).map(ScaUserRespDTO::getId).collect(Collectors.toList());
        }
        return null;
    }
}