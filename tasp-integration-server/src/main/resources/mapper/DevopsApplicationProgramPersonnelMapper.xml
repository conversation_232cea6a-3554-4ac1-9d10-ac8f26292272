<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trinasolar.integration.dao.DevopsApplicationProgramPersonnelMapper">


    <select id="getCountAppByUserId" resultType="java.lang.Integer">
        select count(distinct(program_id)) from devops_application_program_personnel where del_flag = 0 and  user_id = #{userId}
    </select>
</mapper>
