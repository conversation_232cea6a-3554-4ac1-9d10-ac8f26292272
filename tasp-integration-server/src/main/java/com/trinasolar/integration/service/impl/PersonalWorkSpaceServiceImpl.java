package com.trinasolar.integration.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trinasolar.integration.api.dto.User;
import com.trinasolar.integration.api.entity.UserDashboardConfigDO;
import com.trinasolar.integration.controller.personal.*;
import com.trinasolar.integration.controller.workitem.WorkItemBO;
import com.trinasolar.integration.controller.workitem.WorkItemPage;
import com.trinasolar.integration.controller.workitem.WorkItemReq;
import com.trinasolar.integration.controller.workitem.WorkItemType;
import com.trinasolar.integration.dao.*;
import com.trinasolar.integration.service.PersonalWorkspaceService;
import com.trinasolar.integration.util.RequestUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PersonalWorkSpaceServiceImpl implements PersonalWorkspaceService {

    @Autowired
    private UserAccessConfigMapper userAccessConfigMapper;

    @Autowired
    private UserDashboardConfigMapper userDashboardConfigMapper;

    @Autowired
    private UserAppMapper userAppMapper;

    @Autowired
    private DevopsApplicationProgramPersonnelMapper appProgramPersonnelMapper;

    @Autowired
    private FlowInstanceMapper flowInstanceMapper;

    @Override
    public User getUserInfo() {
        return RequestUtils.getCurrentUser();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveUserConfig(UerPersonalConfigBO configBO) {
        if (Objects.nonNull(configBO) && Objects.nonNull(configBO.getUserId())) {
            User currentUser = RequestUtils.getCurrentUser();
            LocalDateTime now = LocalDateTime.now();
            // 快捷入口
            if (CollectionUtil.isNotEmpty(configBO.getAccessConfigs())){
                userAccessConfigMapper.deleteByUserId(configBO.getUserId());
                configBO.getAccessConfigs().forEach(access -> {
                    access.setUserId(configBO.getUserId());
                    access.setCreator(currentUser.getUsername());
                    access.setUpdater(currentUser.getUsername());
                    access.setUpdateTime(now);
                    access.setCreateTime(now);
                    access.setAccessType(3);
                });
                userAccessConfigMapper.insertBatch(configBO.getAccessConfigs());
            }

            // 工作项设置和模块显示设置
            if (CollectionUtil.isNotEmpty(configBO.getWorkItems()) || CollectionUtil.isNotEmpty(configBO.getModuleShows())){
                userDashboardConfigMapper.deleteByUserId(configBO.getUserId());
                UserDashboardConfigDO dashboardConfig = new UserDashboardConfigDO();
                dashboardConfig.setUserId(configBO.getUserId());
                dashboardConfig.setCreator(currentUser.getUsername());
                dashboardConfig.setUpdater(currentUser.getUsername());
                dashboardConfig.setUpdateTime(now);
                dashboardConfig.setCreateTime(now);
                dashboardConfig.setWorkItem(JSON.toJSONString(configBO.getWorkItems()));
                dashboardConfig.setModuleShow(JSON.toJSONString(configBO.getModuleShows()));
                userDashboardConfigMapper.insert(dashboardConfig);
            }
            return true;
        }
        return false;
    }

    @Override
    public UerPersonalConfigBO getUserConfig(String userId) {
        UerPersonalConfigBO configBO = new UerPersonalConfigBO();
        if (Objects.nonNull(userId)) {
            configBO.setUserId(userId);
            // 快捷入口
            List<UserAccessConfigBO> accessConfigs = userAccessConfigMapper.selectByUserId(userId);
            if (CollectionUtil.isEmpty(accessConfigs)){
                accessConfigs = userAccessConfigMapper.selectDefaultConfig(2);
            }
            configBO.setAccessConfigs(accessConfigs);

            // 工作项设置和模块显示设置
            UserDashboardConfigDO dashboardConfig = userDashboardConfigMapper.selectByUserId(userId);
            if (Objects.nonNull(dashboardConfig)) {
                configBO.setWorkItems(JSON.parseArray(dashboardConfig.getWorkItem(), TagConfigBO.class));
                configBO.setModuleShows(JSON.parseArray(dashboardConfig.getModuleShow(), TagConfigBO.class));
            }else {
                // 初始化数据（产品固定为以下内容，不会变动）
                configBO.setWorkItems(builderWorkItems());
                configBO.setModuleShows(builderModule());
            }
        }
        return configBO;
    }

    @Override
    public UerPersonalInfoBO getPersonalWorkspace() {
        UerPersonalInfoBO infoBO = new UerPersonalInfoBO();
        User user = RequestUtils.getCurrentUser();
        if (Objects.nonNull(user)) {
            boolean isAdmin = user.getAdmin();
            if (!isAdmin) {
                infoBO.setSystemNum(userAppMapper.getCountAppByUserId(Long.valueOf(user.getId())));
                infoBO.setProgramNum(appProgramPersonnelMapper.getCountAppByUserId(Long.valueOf(user.getId())));
                // 快捷入口
                List<UserAccessConfigBO> accessConfigs = userAccessConfigMapper.selectByUserId(user.getId());
                if (CollectionUtil.isEmpty(accessConfigs)){
                    accessConfigs = userAccessConfigMapper.selectDefaultConfig(2);
                }
                infoBO.setAccessConfigs(accessConfigs);
                // 文档浏览记录
                infoBO.setDocumentBrowses(Arrays.asList(new DocumentBrowseBO(4071268107809984512L, "testttt",  LocalDateTime.now(), "刘顺")));
            }else {
                // 快捷入口
                infoBO.setAccessConfigs(userAccessConfigMapper.selectDefaultConfig(1));
                // 统计模块区域
                Map<String, List<String>> statModule = new HashMap<>();
                statModule.put("PaaS服务申请", List.of());
                statModule.put("API订阅", List.of());
                statModule.put("文档阅读", List.of());
                statModule.put("技术组件访问", List.of());
                List<FlowInstanceCountBO> flowInstanceCounts = flowInstanceMapper.getFlowInstanceCount();
                if (CollectionUtil.isNotEmpty(flowInstanceCounts)) {
                    Map<String, List<String>> flowInstanceCountMap = flowInstanceCounts.stream().collect(Collectors.groupingBy(FlowInstanceCountBO::getBusinessType, Collectors.mapping(FlowInstanceCountBO::getInstanceName, Collectors.toList())));
                    statModule.put("PaaS服务申请", flowInstanceCountMap.getOrDefault("paas_server", new ArrayList<>()));
                    statModule.put("API订阅", flowInstanceCountMap.getOrDefault("api_order", new ArrayList<>()));
                }
                infoBO.setStatModule(statModule);
            }
            // 最近访问程序
            infoBO.setAppPrograms(Arrays.asList(new AppProgramBO(602L,1L,"知识库服务", "tasp-knowledge", "刘顺", "https://code.trinasolar.com/ipd/tasp/kepler-ui.git")));
            // 最近访问系统
            infoBO.setAppSystems(Arrays.asList(new AppSystemBO(1L, "应用服务平台", "tasp", "建设中", "BPO", "刘顺")));
        }
        return infoBO;
    }

    @Override
    public Page<NoticeBO> noticePage(Page<NoticeBO> page, PageReq req) {
        page.setSize(Objects.isNull(req.getSize()) ? 5 : req.getSize());
        page.setCurrent(Objects.isNull(req.getPage()) ? 1 : req.getPage());
        page.setOptimizeCountSql(false);
        return userAccessConfigMapper.noticePage(page, req);
    }

    @Override
    public WorkItemPage<WorkItemBO> getPersonalWorkItem(WorkItemReq req) {
        List<WorkItemBO> workItemList = new ArrayList<>();
        if ("5".equals(req.getType())) {
            WorkItemBO test = new WorkItemBO();
            test.setTag("Story");
            test.setTitle("测试");
            test.setCreator("刘顺");
            test.setStatus("待处理");
            test.setCreateTime(new Date());
            test.setAppSystem("应用服务");
            test.setDwellTime("5天5小时20分钟");
            test.setAppProgram("应用程序");
            workItemList.add(test);
            WorkItemBO test1 = new WorkItemBO();
            test1.setTag("Feature");
            test1.setTitle("测试");
            test1.setCreator("刘顺");
            test1.setStatus("待测试");
            test1.setCreateTime(new Date());
            test1.setAppSystem("应用服务");
            test1.setDwellTime("5天5小时20分钟");
            test1.setAppProgram("应用程序");
            workItemList.add(test);
            workItemList.add(test1);
        }

        if ("6".equals(req.getType())) {
            WorkItemBO test = new WorkItemBO();
            test.setTag("Task");
            test.setTitle("测试");
            test.setCreator("刘顺");
            test.setStatus("待处理");
            test.setCreateTime(new Date());
            test.setAppSystem("应用服务");
            test.setDwellTime("5天5小时20分钟");
            test.setAppProgram("应用程序");
            workItemList.add(test);
            WorkItemBO test1 = new WorkItemBO();
            test1.setTag("Task");
            test1.setTitle("测试");
            test1.setCreator("刘顺");
            test1.setStatus("待测试");
            test1.setCreateTime(new Date());
            test1.setAppSystem("应用服务");
            test1.setDwellTime("5天5小时20分钟");
            test1.setAppProgram("应用程序");
            workItemList.add(test);
            workItemList.add(test1);
        }

        if ("7".equals(req.getType())) {
            WorkItemBO test = new WorkItemBO();
            test.setTag("Bug");
            test.setTitle("测试");
            test.setCreator("刘顺");
            test.setStatus("待处理");
            test.setCreateTime(new Date());
            test.setAppSystem("应用服务");
            test.setDwellTime("5天5小时20分钟");
            test.setAppProgram("应用程序");
            workItemList.add(test);
            WorkItemBO test1 = new WorkItemBO();
            test1.setTag("Bug");
            test1.setTitle("测试");
            test1.setCreator("刘顺");
            test1.setStatus("待测试");
            test1.setCreateTime(new Date());
            test1.setAppSystem("应用服务");
            test1.setDwellTime("5天5小时20分钟");
            test1.setAppProgram("应用程序");
            workItemList.add(test);
            workItemList.add(test1);
        }

        if ("8".equals(req.getType())) {
            WorkItemBO test = new WorkItemBO();
            test.setTag("中");
            test.setTitle("【111】测试");
            test.setCreator("刘顺");
            test.setStatus("处理中");
            test.setCreateTime(new Date());
            test.setAppSystem("IT请求故障申报");
            workItemList.add(test);
            WorkItemBO test1 = new WorkItemBO();
            test1.setTag("高");
            test1.setTitle("【333】测试");
            test1.setCreator("刘顺");
            test1.setStatus("已提交");
            test1.setCreateTime(new Date());
            test1.setAppSystem("IT请求故障申报");
            workItemList.add(test);
            workItemList.add(test1);
        }

        if ("9".equals(req.getType())) {
            WorkItemBO test = new WorkItemBO();
            test.setTag("#8992 releas->master");
            test.setTitle("合并请求");
            test.setCreator("刘顺");
            test.setStatus("待处理");
            test.setCreateTime(new Date());
            test.setAppSystem("应用服务");
            test.setDwellTime("5天5小时20分钟");
            test.setAppProgram("应用程序");
            workItemList.add(test);
            WorkItemBO test1 = new WorkItemBO();
            test1.setTag("#8992 releas->master");
            test1.setTitle("测合并请求试");
            test1.setCreator("刘顺");
            test1.setStatus("待测试");
            test1.setCreateTime(new Date());
            test1.setAppSystem("应用服务");
            test1.setDwellTime("5天5小时20分钟");
            test1.setAppProgram("应用程序");
            workItemList.add(test);
            workItemList.add(test1);
        }
        if ("1".equals(req.getType()) || "2".equals(req.getType()) || "3".equals(req.getType()) || "4".equals(req.getType())) {
            WorkItemBO test = new WorkItemBO();
            test.setTitle("测试");
            test.setCreator("刘顺");
            test.setStatus("审批中");
            test.setType("PaaS服务申请");
            test.setCreateTime(new Date());
            test.setAppSystem("应用服务");
            test.setTag("task");
            test.setDwellTime("5天5小时20分钟");
            test.setAppProgram("应用程序");
            workItemList.add(test);
            workItemList.add(test);
        }
        WorkItemPage<WorkItemBO> page = new WorkItemPage<>(req.getPage(), req.getSize(), 10, workItemList);
        return page;
    }

    private List<TagConfigBO> builderModule() {
        return new ArrayList<>(){{
            add(new TagConfigBO("个人简介", 1, true));
            add(new TagConfigBO("工作事项", 2, true));
            add(new TagConfigBO("文档收藏", 3, true));
            add(new TagConfigBO("快捷入口", 4, true));
            add(new TagConfigBO("最近访问应用系统", 5, true));
            add(new TagConfigBO("最近访问应用程序", 6, true));
        }};
    }

    private List<TagConfigBO> builderWorkItems() {
        return new ArrayList<>(){{
            add(new TagConfigBO("PaaS", 1, true));
            add(new TagConfigBO("存储", 2, true));
            add(new TagConfigBO("文档", 3, true));
            add(new TagConfigBO("API", 4, true));
            add(new TagConfigBO("需求", 5, true));
            add(new TagConfigBO("任务", 6, true));
            add(new TagConfigBO("缺陷", 7, true));
            add(new TagConfigBO("TIS工单", 8, true));
            add(new TagConfigBO("代码合并请求", 9, true));
        }};
    }
}
