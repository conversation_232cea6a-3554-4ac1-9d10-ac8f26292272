package com.trinasolar.integration.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trinasolar.integration.api.dto.User;
import com.trinasolar.integration.controller.personal.NoticeBO;
import com.trinasolar.integration.controller.personal.PageReq;
import com.trinasolar.integration.controller.personal.UerPersonalConfigBO;
import com.trinasolar.integration.controller.personal.UerPersonalInfoBO;
import com.trinasolar.integration.controller.workitem.WorkItemBO;
import com.trinasolar.integration.controller.workitem.WorkItemPage;
import com.trinasolar.integration.controller.workitem.WorkItemReq;
import com.trinasolar.integration.service.PersonalWorkspaceService;
import com.trinasolar.tasc.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.trinasolar.tasc.framework.common.pojo.CommonResult;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 */
@Tag(name = "个人工作台")
@RestController
@RequestMapping("/personal/workspace")
@Slf4j
public class PersonalWorkspaceController {

    @Autowired
    private PersonalWorkspaceService personalWorkspaceService;

    /**
     * 获取个人信息
     * @param
     * @return
     */
    @GetMapping("/userInfo")
    @Operation(summary = "获取个人信息")
    public CommonResult<User> userInfo() {
        return CommonResult.success(personalWorkspaceService.getUserInfo());
    }

    /**
     * 个性化配置保存
     * @param uerPersonalConfigBO
     * @return
     */
    @PostMapping("/saveConfig")
    @Operation(summary = "个性化配置保存")
    public CommonResult<Boolean> saveUserConfig(@RequestBody UerPersonalConfigBO uerPersonalConfigBO) {
        if (!personalWorkspaceService.saveUserConfig(uerPersonalConfigBO)) {
            return CommonResult.error(GlobalErrorCodeConstants.BAD_REQUEST.getCode(),"配置失败");
        }
        return CommonResult.success(true);
    }

    /**
     * 个性化配置查询
     * @param userId
     * @return
     */
    @GetMapping("/getConfig/{userId}")
    @Operation(summary = "个性化配置保持")
    public CommonResult<UerPersonalConfigBO> getUserConfig(@ApiParam(value = "用户Id", required = true) @PathVariable String userId) {
        return CommonResult.success(personalWorkspaceService.getUserConfig(userId));
    }

    /**
     * 个人工作台信息查询
     * @return
     */
    @GetMapping("/info")
    @Operation(summary = "个人工作台信息查询")
    public CommonResult<UerPersonalInfoBO> getPersonalWorkspace() {
        return CommonResult.success(personalWorkspaceService.getPersonalWorkspace());
    }

    /**
     * 通知公告分页查询
     */
    @PostMapping(value = "/notice")
    @ApiOperation(value = "分页查询")
    public CommonResult<Page<NoticeBO>> noticePage(Page<NoticeBO> page, @RequestBody PageReq req){
        return CommonResult.success(personalWorkspaceService.noticePage(page, req));
    }


    /**
     * 工作事项查询
     * @return
     */
    @PostMapping("/workItem")
    @Operation(summary = "工作事项查询")
    public CommonResult<WorkItemPage<WorkItemBO>> getPersonalWorkItem(@RequestBody WorkItemReq req) {
        return CommonResult.success(personalWorkspaceService.getPersonalWorkItem(req));
    }

}
