package com.trinasolar.integration.service;


import com.trinasolar.integration.api.entity.ProjectConfigDO;
import com.trinasolar.integration.controller.projectconfig.vo.ProjectConfigSaveReqVO;

import java.util.Map;

/**
 * 项目配置信息 Service 接口
 *
 * <AUTHOR>
 */
public interface ProjectConfigService {


    /**
     * @param appSystemId
     * @return
     */
    Boolean initCheck(Long appSystemId);

    /**
     * @param projectId
     * @return
     */
    ProjectConfigDO selectProjectConfigByProjectId(Long projectId);

    ProjectConfigDO selectById(Long id);

    ProjectConfigDO selectByProjectIdAndConfigKey(ProjectConfigSaveReqVO reqVO);

    void addProjectEnvConfigs(Map<String, String> envs, Long systemId);

    void saveProjectConfig(String originData, Long systemId, String busDomain);

}
