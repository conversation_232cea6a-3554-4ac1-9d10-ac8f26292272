<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.5.3</version> <!-- 从2.7.18升级到3.5.3 -->
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
    <groupId>com.trinasolar.integration</groupId>
    <artifactId>kepler-integration</artifactId>
    <version>1.0.0</version>
    <name>kepler-integration</name>
    <description>kepler-integration</description>
    <packaging>pom</packaging>
    <properties>
        <java.version>17</java.version> <!-- 从11升级到17 -->
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <flatten-maven-plugin.version>1.6.0</flatten-maven-plugin.version>
        <!-- 统一依赖管理 -->
        <spring.framework.version>6.2.8</spring.framework.version> <!-- Spring 6.x -->
        <spring.boot.version>3.5.3</spring.boot.version>
        <spring.cloud.version>2025.0.0</spring.cloud.version> <!-- Spring Cloud 2023.x -->
        <spring.cloud.alibaba.version>2023.0.3.3</spring.cloud.alibaba.version> <!-- Spring Cloud Alibaba 2023.x -->
        <!-- 其他依赖版本也需要更新到兼容Spring Boot 3.x的版本 -->
        <!-- Web 相关 -->
        <springdoc.version>1.6.15</springdoc.version>
        <knife4j.version>4.4.0</knife4j.version>
        <!-- DB 相关 -->
        <druid.version>1.2.24</druid.version>
        <mybatis.version>3.5.17</mybatis.version>
        <mybatis-plus.version>3.5.9</mybatis-plus.version>
        <dynamic-datasource.version>4.3.1</dynamic-datasource.version>
        <mybatis-plus-join.version>1.4.13</mybatis-plus-join.version>
        <easy-trans.version>3.0.6</easy-trans.version>
        <caffeine.version>2.9.3</caffeine.version>
        <redisson.version>3.41.0</redisson.version>
        <dm8.jdbc.version>8.1.3.140</dm8.jdbc.version>
        <kingbase.jdbc.version>8.6.0</kingbase.jdbc.version>
        <opengauss.jdbc.version>5.1.0</opengauss.jdbc.version>
        <!-- 消息队列 -->
        <rocketmq-spring.version>2.3.1</rocketmq-spring.version>
        <!-- RPC 相关 -->
        <!-- Config 配置中心相关 -->
        <!-- Job 定时任务相关 -->
        <xxl-job.version>2.4.0</xxl-job.version>
        <!-- 服务保障相关 -->
        <lock4j.version>2.2.7</lock4j.version>
        <!-- 监控相关 -->
        <skywalking.version>8.12.0</skywalking.version>
        <spring-boot-admin.version>2.7.15</spring-boot-admin.version>
        <opentracing.version>0.33.0</opentracing.version>
        <!-- Test 测试相关 -->
        <podam.version>7.2.11.RELEASE</podam.version> <!-- Spring Boot 2.X 最多使用 7.2.11 版本 -->
        <jedis-mock.version>1.1.4</jedis-mock.version>
        <mockito-inline.version>4.11.0</mockito-inline.version>
        <!-- Bpm 工作流相关 -->
        <flowable.version>6.8.0</flowable.version>
        <!-- 工具类相关 -->
        <captcha-plus.version>1.0.8</captcha-plus.version>
        <jsoup.version>1.18.1</jsoup.version>
        <lombok.version>1.18.36</lombok.version>
        <mapstruct.version>1.5.3.Final</mapstruct.version>
        <hutool.version>5.8.35</hutool.version>
        <easyexcel.verion>4.0.3</easyexcel.verion>
        <velocity.version>2.4</velocity.version> <!-- JDK8 不能从 2.4 升级到 2.4.1，会报包不存在！！！！ -->
        <fastjson.version>2.0.48</fastjson.version>
        <guava.version>33.4.0-jre</guava.version>
        <transmittable-thread-local.version>2.14.5</transmittable-thread-local.version>
        <commons-net.version>3.11.1</commons-net.version>
        <jsch.version>0.1.55</jsch.version>
        <tika-core.version>2.9.2</tika-core.version>
        <ip2region.version>2.7.0</ip2region.version>
        <bizlog-sdk.version>3.0.6</bizlog-sdk.version>
        <reflections.version>0.10.2</reflections.version>
        <netty.version>4.1.116.Final</netty.version>
        <!-- 三方云服务相关 -->
        <commons-io.version>2.17.0</commons-io.version>
        <commons-compress.version>1.27.1</commons-compress.version>
        <aws-java-sdk-s3.version>1.12.777</aws-java-sdk-s3.version>
        <justauth.version>1.0.8</justauth.version>
        <jimureport.version>1.7.8</jimureport.version>
        <weixin-java.version>4.6.0</weixin-java.version>
        <logback.version>1.5.18</logback.version> <!-- 无法使用 1.3.X 版本，启动会报错 -->
        <slf4j.version>2.0.16</slf4j.version>
        <jakarta.servlet-api.version>6.0.0</jakarta.servlet-api.version>
        <httpclient5.version>5.4.4</httpclient5.version>
        <mysql.connector.version>8.2.0</mysql.connector.version>
    </properties>
    <modules>
        <module>tasp-integration-framework</module>
        <module>tasp-integration-server</module>
        <module>tasp-integration-api</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.trinasolar</groupId>
                <artifactId>trina-oplog-starter</artifactId>
                <version>4.0.9</version>
            </dependency>
            <dependency>
                <groupId>com.trinasolar.integration</groupId>
                <artifactId>tasp-springboot-starter-web</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.trinasolar.integration</groupId>
                <artifactId>tasp-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents.client5</groupId>
                <artifactId>httpclient5</artifactId>
                <version>${httpclient5.version}</version>
            </dependency>
            <dependency>
                <groupId>com.trinasolar.integration</groupId>
                <artifactId>tasp-spingboot-starter-mybatis</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.trinasolar.integration</groupId>
                <artifactId>tasp-integration-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.trinasolar.integration</groupId>
                <artifactId>tasp-springboot-starter-kafka</artifactId>
                <version>${project.version}</version>
            </dependency>
            <!-- 统一依赖管理 -->
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-bom</artifactId>
                <version>${netty.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-framework-bom</artifactId> <!-- JDK8 版本独有：保证 Spring Framework 尽量高 -->
                <version>${spring.framework.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring.boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring.cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring.cloud.alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>


            <!-- Spring 核心 -->
            <dependency>
                <!-- 用于生成自定义的 Spring @ConfigurationProperties 配置类的说明文件 -->
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-configuration-processor</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>


            <dependency>
                <groupId>org.springdoc</groupId> <!-- 接口文档 UI：默认 -->
                <artifactId>springdoc-openapi-ui</artifactId>
                <version>${springdoc.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>javax.servlet</groupId>
                        <artifactId>javax.servlet-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.github.xiaoymin</groupId> <!-- 接口文档 UI：knife4j -->
                <artifactId>knife4j-openapi3-spring-boot-starter</artifactId>
                <version>${knife4j.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>javax.servlet</groupId>
                        <artifactId>javax.servlet-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- DB 相关 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-3-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>${mybatis.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-jsqlparser-4.9</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>com.mysql</groupId>
                <artifactId>mysql-connector-j</artifactId>
                <version>${mysql.connector.version}</version>
            </dependency>


            <dependency>
                <groupId>com.github.yulichang</groupId>
                <artifactId>mybatis-plus-join-boot-starter</artifactId> <!-- MyBatis 联表查询 -->
                <version>${mybatis-plus-join.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId> <!-- 代码生成器，使用它解析表结构 -->
                <version>${mybatis-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot3-starter</artifactId>
                <version>${dynamic-datasource.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fhs-opensource</groupId>
                <artifactId>easy-trans-anno</artifactId>
                <version>${easy-trans.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-inline</artifactId>
                <version>${mockito-inline.version}</version> <!-- 支持 Mockito 的 final 类与 static 方法的 mock -->
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <version>${spring.boot.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>asm</artifactId>
                        <groupId>org.ow2.asm</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.mockito</groupId>
                        <artifactId>mockito-core</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId> <!-- use mapstruct-jdk8 for Java 8 or higher -->
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.verion}</version>
            </dependency>

            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-compress</artifactId>
                <version>${commons-compress.version}</version>
            </dependency>


            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId> <!-- 解决 ThreadLocal 父子线程的传值问题 -->
                <version>${transmittable-thread-local.version}</version>
            </dependency>


            <dependency>
                <groupId>commons-codec</groupId>
                <artifactId>commons-codec</artifactId>
                <version>1.15</version>
            </dependency>
            <dependency>
                <groupId>org.jsoup</groupId>
                <artifactId>jsoup</artifactId>
                <version>${jsoup.version}</version>
            </dependency>

            <!-- 专属于 JDK8 安全漏洞升级 -->
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-core</artifactId>
                <version>${logback.version}</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
                <version>${logback.version}</version>
            </dependency>
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-actuator</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.redisson</groupId>
                        <artifactId>redisson-spring-data-33</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-data-27</artifactId>
                <version>${redisson.version}</version>
            </dependency>

            <!--server-api-->
            <dependency>
                <groupId>jakarta.servlet</groupId>
                <artifactId>jakarta.servlet-api</artifactId>
                <version>${jakarta.servlet-api.version}</version>
            </dependency>


        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <annotationProcessorPaths>
                        <!-- 必须放在lombok之前 -->
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${mapstruct.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <!--        <distributionManagement>-->
    <!--            <repository>-->
    <!--                <id>maven-releases</id>-->
    <!--                <name>rdc-releases</name>-->
    <!--                <url>http://10.10.101.92:8081/repository/maven-releases/</url>-->
    <!--            </repository>-->
    <!--            <snapshotRepository>-->
    <!--                <id>maven-snapshots</id>-->
    <!--                <name>rdc-snapshots</name>-->
    <!--                <url>http://10.10.101.92:8081/repository/maven-snapshots/</url>-->
    <!--            </snapshotRepository>-->
    <!--        </distributionManagement>-->

    <!--        <repositories>-->
    <!--                <repository>-->
    <!--                    <id>maven-releases</id>-->
    <!--                    <name>rdc-releases</name>-->
    <!--                    <url>http://10.10.101.92:8081/repository/maven-releases/</url>-->
    <!--                </repository>-->
    <!--        </repositories>-->
    <repositories>
        <repository>
            <id>trina-public</id>
            <name>Trina Public Repository</name>
            <url>https://bizdevops-repo.trinasolar.com/maven/g6ac9f/maven-public</url>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>daily</updatePolicy>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </repository>
    </repositories>
<!--    <distributionManagement>-->
<!--        <repository>-->
<!--            <id>nexus-releases</id>-->
<!--            <name>repository-releases</name>-->
<!--            <url>http://10.40.1.96:18081/nexus/content/repositories/releases/</url>-->
<!--        </repository>-->
<!--        <snapshotRepository>-->
<!--            <id>nexus-snapshots</id>-->
<!--            <name>repository-snapshots</name>-->
<!--            <url>http://10.40.1.96:18081/nexus/content/repositories/snapshots/</url>-->
<!--        </snapshotRepository>-->
<!--    </distributionManagement>-->

</project>
