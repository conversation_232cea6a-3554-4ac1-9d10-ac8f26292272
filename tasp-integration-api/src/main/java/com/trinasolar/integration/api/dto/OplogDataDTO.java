package com.trinasolar.integration.api.dto;

import lombok.Data;

@Data
public class OplogDataDTO {
    /**
     * 日志操作时间
     */
    private String createTime;
    /**
     * 日志唯一ID
     */
    private String logId;
    /**
     * 操作人ID
     */
    private String operatorId;
    /**
     * 操作人名称
     */
    private String operatorName;
    /**
     * 应用名称
     */
    private String appName;

    /**
     * 服务名称
     */
    private String serviceName;

    /**
     * 环境：dev/test/uat/prod
     */
    private String env;

    /**
     * 租户ID（多租户场景）
     */
    private String tenantId;
    /**
     * 追踪ID
     */
    private String traceId;

    /**
     * 客户端IP
     */
    private String clientIp;

    /**
     * 用户代理
     */
    private String userAgent;

    /**
     * 请求路径
     */
    private String requestUri;

    /**
     * 请求方法：GET/POST/PUT/DELETE等
     */
    private String requestMethod;

    /**
     * 请求版本号
     */
    private String requestVersion;

    /**
     * 请求参数（queryParam或JSON格式）
     */
    private String requestParam;

    /**
     * 业务类型
     */
    private String bizType;
    /**
     * 日志标签
     */
    private String tag;
    /**
     * 日志内容
     */
    private String content;
    /**
     * 方法是否成功
     */
    private Boolean success;
    /**
     * 响应数据（关键信息，JSON格式）,统一保存正常和异常结果
     */
    private String responseData;
    /**
     * 耗时（单位：毫秒）
     */
    private Long costTime;
    /**
     * 额外信息（JSON格式）
     */
    private String extra;
    /**
     * 实体DIFF列表（JSON格式）
     */
    private String diffListStr;
}
