package com.trinasolar.integration.api.config;

import feign.Client;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.TrustSelfSignedStrategy;
import org.apache.http.ssl.SSLContextBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.net.ssl.SSLContext;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;

/**
 * <AUTHOR>
 */
@Configuration
public class FeignSslConfig {

    @Bean
    public Client feignClient() throws NoSuchAlgorithmException, KeyStoreException, KeyManagementException {
        // 创建SSL上下文，信任所有证书
        SSLContext sslContext = new SSLContextBuilder()
                .loadTrustMaterial(null, new TrustSelfSignedStrategy())
                .build();

        // 禁用主机名验证
        return new Client.Default(sslContext.getSocketFactory(), NoopHostnameVerifier.INSTANCE);
    }
}
