package com.trinasolar.integration.controller;

import cn.hutool.core.util.StrUtil;
import com.trinasolar.integration.api.dto.SystemSyncReqDTO;
import com.trinasolar.integration.api.entity.AppSystem;
import com.trinasolar.integration.api.entity.ApplicationProgram;
import com.trinasolar.integration.dao.AppSystemMapper;
import com.trinasolar.integration.service.AppService;
import com.trinasolar.tasc.framework.common.pojo.CommonResult;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Tag(name = "管理后台 - 应用管理")
@RestController
@RequestMapping("/integration/app")
public class AppController {

    @Autowired
    AppService appService;

    @Autowired
    private AppSystemMapper appSystemMapper;

    /**
     * 同步应用程序下游系统（页面创建应用程序时，Program服务调用）
     */
    @PostMapping("/create")
    public CommonResult<Boolean> createApp(@RequestBody ApplicationProgram applicationProgram) {
        AppSystem appSystem = appSystemMapper.selectById(applicationProgram.getApplicationId());
        String syncNextSystem = appSystem.getSyncNextSystem();
        log.info("同步下游系统:{}", syncNextSystem);
        if (StrUtil.isNotBlank(syncNextSystem)) {
            String[] split = syncNextSystem.split(",");
            for (int i = 0; i < split.length; i++) {
                appService.createApp(applicationProgram, new SystemSyncReqDTO(applicationProgram.getApplicationId(), split[i]));
            }
        }
        return CommonResult.success(true);
    }

    /**
     * 同步应用程序下游系统（cloud）导入时调用）
     */
    @PostMapping("/create/downStream")
    public CommonResult<Boolean> createAppV2(@RequestBody ApplicationProgram applicationProgram) {
        String nextSyncSystem = applicationProgram.getSyncNextSystem();
        log.info("同步下游系统:{}", nextSyncSystem);
        if (StrUtil.isNotBlank(nextSyncSystem)) {
            String[] split = nextSyncSystem.split(",");
            for (int i = 0; i < split.length; i++) {
                appService.createApp(applicationProgram, new SystemSyncReqDTO(applicationProgram.getApplicationId(), split[i]));
            }
        }
        return CommonResult.success(true);
    }

    @GetMapping("/initProgramGit")
    public CommonResult<Boolean> initProgramGit() {
        return CommonResult.success(appService.initProgramGit());
    }
}
