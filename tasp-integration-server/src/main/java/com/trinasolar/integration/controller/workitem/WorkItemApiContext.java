package com.trinasolar.integration.controller.workitem;

public class WorkItemApiContext {
    private WorkItemApiStrategy workItemApiStrategy;

    public WorkItemApiContext(WorkItemApiStrategy workItemApiStrategy) {
        this.workItemApiStrategy = workItemApiStrategy;
    }

    public WorkItemPage<WorkItemBO> executeStrategy() {
        return workItemApiStrategy.processApi();
    }

    public void setWorkItemApiStrategy(WorkItemApiStrategy workItemApiStrategy) {
        this.workItemApiStrategy = workItemApiStrategy;
    }
}
