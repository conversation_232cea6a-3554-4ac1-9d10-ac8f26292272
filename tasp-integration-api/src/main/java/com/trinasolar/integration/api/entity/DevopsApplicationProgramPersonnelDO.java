package com.trinasolar.integration.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@TableName("devops_application_program_personnel")
@Data
@Accessors(chain = true)
public class DevopsApplicationProgramPersonnelDO {

    @TableId(type = IdType.AUTO)
    private Long id;

    // 关联应用程序ID
    @TableField("program_id")
    private Long programId;

    @TableField("personnel_type")
    private String personnelType;

    @TableField("user_id")
    private Long userId;

    @TableField("role_id")
    private String roleId;

    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @TableField("del_flag")
    private Integer delFlag;
}
