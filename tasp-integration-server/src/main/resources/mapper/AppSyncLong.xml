<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trinasolar.integration.dao.AppSyncLogMapper">

<!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.trinasolar.integration.api.entity.AppSyncLog">
        <id column="id" property="id"/>
        <result column="app_id" property="appId"/>
        <result column="app_name" property="appName"/>
        <result column="down_stream_name" property="downStreamName"/>
        <result column="creator_id" property="creatorId"/>
        <result column="creator_name" property="creatorName"/>
        <result column="created_time" property="createdTime"/>
  </resultMap>


</mapper>
