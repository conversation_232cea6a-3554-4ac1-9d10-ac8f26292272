package com.trinasolar.integration.controller.external;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @description TODO
 * @since 2019/5/17 11:06
 */
@Builder
@ToString
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "应用详情")
public class AppDetailVO implements Serializable {

    private static final long serialVersionUID = -3549453137994951985L;

    @ApiModelProperty(value = "应用ID")
    private Long id;

    @ApiModelProperty(value = "过期时间")
    private LocalDateTime expireTime;

    @ApiModelProperty(value = "应用中文名称")
    private String cnName;
    private String cnSimpleName;

    @ApiModelProperty(value = "应用英文名称")
    private String enName;

    private String enSimpleName;

    @ApiModelProperty(value = "应用观云台名称")
    private String cloudName;

    @ApiModelProperty(value = "应用remark")
    private String remark;

    @ApiModelProperty(value = "应用是否有效CODE")
    private String delFlag;

    @ApiModelProperty(value = "应用下线标识CODE")
    private String lockFlag;

    @ApiModelProperty(value = "应用所处阶段CODE")
    private String stageStatus;

    @ApiModelProperty("负责人ID")
    private Long creatorId;

    @ApiModelProperty(value = "应用创建人")
    private String creatorName;

    @ApiModelProperty(value = "应用创建时间")
    private LocalDateTime createdTime;

    @ApiModelProperty(value = "应用更新时间")
    private LocalDateTime updatedTime;

    @ApiModelProperty(value = "应用访问地址")
    private String appUrl;

    @ApiModelProperty(value = "LOGO url")
    private String logoUrl;

    @ApiModelProperty(value = "图片详情URL")
    private String detailPicUrl;

    @ApiModelProperty(value = "应用UI风格CODE")
    private Long appStyle;

    @ApiModelProperty(value = "应用类型：传统应用、新架构应用")
    private Long appType;

    @ApiModelProperty(value = "应用组织结构ID列表")
    private List<String> OrgIdList;

    @ApiModelProperty(value = "应用审核ID")
    private Long auditId;

    @ApiModelProperty(value = "是否过期")
    private Boolean expire = false;

    @ApiModelProperty("首页地址")
    private String homePage;

    @ApiModelProperty("展示应用首页：0否，1是")
    private Integer showHomepage;

    @ApiModelProperty("管理平台地址")
    private String manageUrl;

    @ApiModelProperty("1 移动端app 0 web 端 app")
    private Integer isMobile;

    @ApiModelProperty("是否支持 ie 浏览器， 0 不支持  1 支持")
    private Integer ieSupported;

    @ApiModelProperty("IE 提示信息富文本")
    private Map ieTips;

    @ApiModelProperty("是否控制功能权限  0 不控制  1 控制")
    private Integer isControlFP;

    @ApiModelProperty("是否控制数据权限  0 不控制  1 控制")
    private Integer isControlDP;

    @ApiModelProperty("同时管控pc,app菜单:1是,0否")
    private Integer isManagePcAppMenu;

    @ApiModelProperty("是否是容器云应用")
    private Integer isContainerApp;

    @ApiModelProperty(notes = "观云台应用所属项目ID")
    private String cloudProjectId;

    @ApiModelProperty(value = "观云台应用所属项目名称")
    private String cloudProjectName;

    @ApiModelProperty(value = "观云台应用所属项目别名")
    private String cloudProjectAliasName;

    @ApiModelProperty(notes = "观云台应用所属租户ID")
    private String cloudTenantId;

    @ApiModelProperty(value = "观云台应用所属租户名称")
    private String cloudTenantName;

    @ApiModelProperty(value = "观云台应用所属租户别名")
    private String cloudTenantAliasName;

    @ApiModelProperty(value = "观云台应用所属分区英文名称")
    private String cloudNamespaceId;

    @ApiModelProperty(value = "观云台应用所属分区名称")
    private String cloudNamespaceName;

    @ApiModelProperty(value = "观云台应用所属分区集群")
    private String cloudClusterId;

    @ApiModelProperty("租户")
    private Integer tenantId;

    @ApiModelProperty("租户组")
    private String tenantGroup;

    @ApiModelProperty("是否账号租户切换")
    private Integer isChangeTenant;

    @ApiModelProperty("业务域")
    private String businessDomain;
    private String businessDomainName;

    private String version;
    /**
     * 应用系统类型【多选】
     */
    private String appSysType;
    /**
     * 应用组
     */
    private String appGroup;


    /**
     * 应用服务级别
     *
     */
    private String appServiceLevel;

    /**
     * 建设方式
     */
    private String constructionType;

    /**
     * 开发语言【多选】
     */
    private String devLanguage;


    /**
     * 团队名称
     */
    private String teamName;

    /**
     * 各负责人-数据集合【开发、系统、业务、运维】
     */
    private Map<String , List<HashMap<String,String>>> responsibleUsers;
    /**
     * 应用编码
     */
    private String code;
    /**
     * 访问地址
     */
    private String accessUrl;
    /**
     * 使用手册
     */
    private String manualUrl;
    /**
     * 实际上线时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime actuallyTime;
    /**
     * 集群
     */
    private String cluster;
    /**
     * 关联系统
     */
    private String relationSystem;
    /**
     * 节点规格
     */
    private String nodeSpec;
    private Boolean gitlab;
    private Boolean devops;
    private Boolean apm;

    /*************** 新增字段 ****************/
    @ApiModelProperty(value = "应用域，单选，数据字典维护，目前只有“MBT&DT”")
    private String appDomain;

    @ApiModelProperty(value = "应用,单选，数据字典维护，与应用域联动")
    private String appName;

    @ApiModelProperty(value = "产品线，下拉选择框，可选值包括AI产品组、低代码、RPA组、技术平台组 、数据产品组、数据平台组、外购业务应用产品组、外购职能应用产品组、支撑组、自研应用产品组")
    private String productLine;

    @ApiModelProperty(value = "业务版块，下拉选择框，可选值包括储能、光伏、支架、研究院、解决方案、通用")
    @NotNull(message = "请输入业务版块")
    private String bizUnit;

    @ApiModelProperty(value = "业务范围类型，下拉选择框，可选值：营销服、智能制造、其他领域")
    @NotNull(message = "请输入业务范围类型")
    private String bizScopeType;

    @ApiModelProperty(value = "业务范围，下拉选择框，与“业务范围类型”联动")
    @NotNull(message = "请输入业务范围")
    private String bizScope;

    @ApiModelProperty(value = "同步下游系统ID，多选")
    private String syncNextSystem;

    @ApiModelProperty(value = "外采产品名称，当“建设方式”为“采购”时，该项必填")
    private String externalProductName;

    @ApiModelProperty(value = "产品方，当“建设方式”为“采购”时，该项必填")
    private String productVendor;

    @ApiModelProperty(value = "产品实施方，“建设方式”为“采购”时，默认取产品方，可修改")
    private String implVendor;

    @ApiModelProperty(value = "授权方式，当“建设方式”为“采购”时必填，单选，可选值包括:永久、多年、年度")
    private String licenseMode;

    @ApiModelProperty(value = "授权方式说明，对授权方式的补充说明")
    private String licenseDescription;

    @ApiModelProperty(value = "重要性等级，下拉选择框，可选值：一级、二级、三级")
//    @NotNull(message = "请输入重要性等级")
    private String priorityLevel;

    @ApiModelProperty(value = "服务时间，建议格式“7*24小时服务”，“5*8小时服务”")
//    @NotNull(message = "请输入服务时间")
    private String serviceHours;

    @ApiModelProperty(value = "系统定位描述")
    private String description;

    @ApiModelProperty(value = "流程域")
    private String flowDomain;
}
