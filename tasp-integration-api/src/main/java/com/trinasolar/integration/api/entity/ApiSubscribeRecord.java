package com.trinasolar.integration.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName("api_subscribe_record") // 对应数据库表名
public class ApiSubscribeRecord {
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 申请用户id
     */
    private String userId;
    /**
     * Api id
     */
    private Long productId;
    /**
     * 订阅组
     */
    private Long subOrgId;
    /**
     * 请求实例id
     */
    private String instanceId;
    /**
     *  0:申请中 1:通过 2:拒绝
     */
    private Integer status;

    /**
     * 鉴权应用id
     */
    private String clientId;

    /**
     * 鉴权应用名称
     */
    private String newAppName;

    /**
     * 环境信息
     */
    private String env;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}