package com.trinasolar.tasc.framework.apilog.config;

import com.trinasolar.tasc.framework.apilog.core.interceptor.ApiAccessLogInterceptor;
import com.trinasolar.tasc.framework.web.config.TsWebAutoConfiguration;
import jakarta.servlet.Filter;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;


@AutoConfiguration(after = TsWebAutoConfiguration.class)
public class TsApiLogAutoConfiguration implements WebMvcConfigurer {


    private static <T extends Filter> FilterRegistrationBean<T> createFilterBean(T filter, Integer order) {
        FilterRegistrationBean<T> bean = new FilterRegistrationBean<>(filter);
        bean.setOrder(order);
        return bean;
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new ApiAccessLogInterceptor());
    }

}
