package com.trinasolar.integration.config.log;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 */
@Configuration
@Order(Ordered.HIGHEST_PRECEDENCE)
public class OplogConfiguration implements WebMvcConfigurer {

    @Autowired
    private OplogInterceptor oplogInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(oplogInterceptor).addPathPatterns("/**");

    }

}
