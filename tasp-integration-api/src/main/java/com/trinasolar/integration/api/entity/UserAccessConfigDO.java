package com.trinasolar.integration.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户快捷入口配置表
 *
 * <AUTHOR>
 */
@Data
@TableName("tasp_user_access_config")
public class UserAccessConfigDO implements Serializable {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户id
     */
    @TableField(value = "user_id")
    private String userId;

    /**
     * 类型（1：管理员配置；2：开发者配置；3：个性化配置）
     */
    @TableField(value = "access_type")
    private Integer accessType;

    /**
     * logo地址
     */
    @TableField(value = "logo_url")
    private String logoUrl;

    /**
     * 名称
     */
    @TableField(value = "name")
    private String name;

    /**
     * 网址
     */
    @TableField(value = "url")
    private String url;

    /**
     * 排序
     */
    @TableField(value = "sort")
    private Integer sort;

    /**
     * 创建者
     */
    @TableField(value = "creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(value = "updater")
    private String updater;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 是否删除：0：不删除，1：删除
     */
    @TableField(value = "deleted")
    private Integer deleted;
}
