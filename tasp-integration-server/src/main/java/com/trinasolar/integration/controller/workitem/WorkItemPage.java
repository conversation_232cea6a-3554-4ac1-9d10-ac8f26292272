package com.trinasolar.integration.controller.workitem;

import java.util.List;

/**
 * 工作项分页
 * @param <T>
 */
public class WorkItemPage<T> {
    /**
     * 当前页码，默认从1开始
     */
    private int pageNum = 1;

    /**
     * 每页显示的记录数，默认10条
     */
    private int pageSize = 10;

    /**
     * 总记录数
     */
    private long total;

    /**
     * 总页数
     */
    private int totalPages;

    /**
     * 当前页的数据列表
     */
    private List<T> list;

    /**
     * 是否为第一页
     */
    private boolean isFirstPage;

    /**
     * 是否为最后一页
     */
    private boolean isLastPage;

    /**
     * 是否有前一页
     */
    private boolean hasPreviousPage;

    /**
     * 是否有下一页
     */
    private boolean hasNextPage;

    /**
     * 构造方法，初始化分页参数
     * @param pageNum 当前页码
     * @param pageSize 每页记录数
     * @param total 总记录数
     * @param list 当前页数据
     */
    public WorkItemPage(int pageNum, int pageSize, long total, List<T> list) {
        // 校验页码，确保页码不小于1
        this.pageNum = Math.max(pageNum, 1);
        // 校验每页记录数，确保不小于1
        this.pageSize = Math.max(pageSize, 1);
        this.total = total;
        this.list = list;

        // 计算总页数
        calculateTotalPages();
        // 修正页码，防止页码超过总页数
        fixPageNum();
        // 计算分页状态
        calculatePageStatus();
    }

    /**
     * 计算总页数
     */
    private void calculateTotalPages() {
        if (total <= 0) {
            totalPages = 0;
            return;
        }
        totalPages = (int) (total % pageSize == 0 ? total / pageSize : total / pageSize + 1);
    }

    /**
     * 修正页码，确保页码在有效范围内
     */
    private void fixPageNum() {
        if (totalPages == 0) {
            pageNum = 1;
            return;
        }
        if (pageNum > totalPages) {
            pageNum = totalPages;
        }
    }

    /**
     * 计算分页状态（是否第一页、最后一页等）
     */
    private void calculatePageStatus() {
        isFirstPage = pageNum == 1;
        isLastPage = totalPages == 0 || pageNum == totalPages;
        hasPreviousPage = pageNum > 1;
        hasNextPage = totalPages > 0 && pageNum < totalPages;
    }

    /**
     * 获取分页查询的起始位置（用于SQL查询）
     * @return 起始位置
     */
    public int getStartIndex() {
        return (pageNum - 1) * pageSize;
    }

    // getter和setter方法
    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
        fixPageNum();
        calculatePageStatus();
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
        calculateTotalPages();
        fixPageNum();
        calculatePageStatus();
    }

    public long getTotal() {
        return total;
    }

    public void setTotal(long total) {
        this.total = total;
        calculateTotalPages();
        fixPageNum();
        calculatePageStatus();
    }

    public int getTotalPages() {
        return totalPages;
    }

    public List<T> getList() {
        return list;
    }

    public void setList(List<T> list) {
        this.list = list;
    }

    public boolean isFirstPage() {
        return isFirstPage;
    }

    public boolean isLastPage() {
        return isLastPage;
    }

    public boolean isHasPreviousPage() {
        return hasPreviousPage;
    }

    public boolean isHasNextPage() {
        return hasNextPage;
    }
}
