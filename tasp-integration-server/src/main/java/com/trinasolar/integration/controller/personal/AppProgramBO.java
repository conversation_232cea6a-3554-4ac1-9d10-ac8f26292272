package com.trinasolar.integration.controller.personal;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.trinasolar.integration.BaseDO;
import lombok.*;
import org.apache.ibatis.type.JdbcType;

import java.io.Serializable;
import java.util.List;

/**
 * @Description 应用程序
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AppProgramBO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 应用程序的唯一标识
     */
    private Long id;

    /**
     * 应用系统id
     */
    private Long applicationId;

    /**
     * 应用程序中文名称
     */
    private String programNameCn;

    /**
     * 应用程序英文名称
     */
    private String programNameEn;

    /**
     * 开发负责人
     */
    private String developDirector;

    /**
     * gitlab代码仓库
     */
    private String gitlabRepoUrl;
}
