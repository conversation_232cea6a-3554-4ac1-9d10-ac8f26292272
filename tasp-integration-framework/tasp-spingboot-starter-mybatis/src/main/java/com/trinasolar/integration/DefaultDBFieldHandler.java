package com.trinasolar.integration;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.trinasolar.integration.entity.BaseEntity;
import com.trinasolar.tasc.framework.web.core.util.WebFrameworkUtils;
import org.apache.ibatis.reflection.MetaObject;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 通用参数填充实现类
 *
 * 如果没有显式的对通用参数进行赋值，这里会对通用参数进行填充、赋值
 *
 */
public class DefaultDBFieldHandler implements MetaObjectHandler {

    @Override
    public void insertFill(MetaObject metaObject) {
        if (Objects.nonNull(metaObject) && metaObject.getOriginalObject() instanceof BaseDO) {
            BaseDO baseDO = (BaseDO) metaObject.getOriginalObject();

            LocalDateTime current = LocalDateTime.now();
            // 创建时间为空，则以当前时间为插入时间
            if (Objects.isNull(baseDO.getCreateTime())) {
                baseDO.setCreateTime(current);
            }
            // 更新时间为空，则以当前时间为更新时间
            if (Objects.isNull(baseDO.getUpdateTime())) {
                baseDO.setUpdateTime(current);
            }

            Long userId = WebFrameworkUtils.getLoginUserId();
            // 当前登录用户不为空，创建人为空，则当前登录用户为创建人
            if (Objects.nonNull(userId) && Objects.isNull(baseDO.getCreateBy())) {
                baseDO.setCreateBy(userId.toString());
            }
            // 当前登录用户不为空，更新人为空，则当前登录用户为更新人
            if (Objects.nonNull(userId) && Objects.isNull(baseDO.getUpdateBy())) {
                baseDO.setUpdateBy(userId.toString());
            }
        }

        if (Objects.nonNull(metaObject) && metaObject.getOriginalObject() instanceof BaseEntity) {
            BaseEntity baseEntity = (BaseEntity) metaObject.getOriginalObject();

            LocalDateTime current = LocalDateTime.now();
            // 创建时间为空，则以当前时间为插入时间
            if (Objects.isNull(baseEntity.getCreatedTime())) {
                baseEntity.setCreatedTime(current);
            }
            // 更新时间为空，则以当前时间为更新时间
            if (Objects.isNull(baseEntity.getUpdatedTime())) {
                baseEntity.setUpdatedTime(current);
            }

            Long userId = WebFrameworkUtils.getLoginUserId();
            // 当前登录用户不为空，创建人为空，则当前登录用户为创建人
            if (Objects.nonNull(userId) && Objects.isNull(baseEntity.getCreatorId())) {
                baseEntity.setCreatorId(userId);
            }
            // 当前登录用户不为空，更新人为空，则当前登录用户为更新人
            if (Objects.nonNull(userId) && Objects.isNull(baseEntity.getUpdaterId())) {
                baseEntity.setUpdaterId(userId);
            }
            baseEntity.setDeleted(0);
        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        // 更新时间为空，则以当前时间为更新时间
        Object modifyTime = getFieldValByName("updateTime", metaObject);
        if (Objects.isNull(modifyTime)) {
            setFieldValByName("updateTime", LocalDateTime.now(), metaObject);
        }

        Object updateTime = getFieldValByName("updatedTime", metaObject);
        if (Objects.isNull(updateTime)) {
            setFieldValByName("updatedTime", LocalDateTime.now(), metaObject);
        }

        // 当前登录用户不为空，更新人为空，则当前登录用户为更新人
        Long userId = WebFrameworkUtils.getLoginUserId();

        // 处理 BaseDO 的 updateBy 字段
        Object updateBy = getFieldValByName("updateBy", metaObject);
        if (Objects.nonNull(userId) && Objects.isNull(updateBy)) {
            setFieldValByName("updateBy", userId.toString(), metaObject);
        }

        // 处理 AppDO 等其他实体的 updater 字段
        Object updater = getFieldValByName("updater", metaObject);
        if (Objects.nonNull(userId) && Objects.isNull(updater)) {
            setFieldValByName("updater", userId.toString(), metaObject);
        }

        // 处理 BaseEntity 的 updaterId 字段
        Object updaterId = getFieldValByName("updaterId", metaObject);
        if (Objects.nonNull(userId) && Objects.isNull(updaterId)) {
            setFieldValByName("updaterId", userId, metaObject);
        }
    }
}
