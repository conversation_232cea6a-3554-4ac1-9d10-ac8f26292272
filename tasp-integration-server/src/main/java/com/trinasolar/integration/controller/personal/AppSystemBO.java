package com.trinasolar.integration.controller.personal;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trinasolar.integration.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AppSystemBO implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 应用系统id
	 */
	private Long systemId;

	/**
	 * 应用系统中文名称
	 */
	private String cnName;

	/**
	 * 应用系统英文名称
	 */
	private String enName;

	/**
	 * 应用系统阶段状态
	 */
	private String stageStatus;

	/**
	 * 应用系统业务域
	 */
	private String businessDomain;

	/**
	 * 应用系统管理员
	 */
	private String appAdmin;

}
