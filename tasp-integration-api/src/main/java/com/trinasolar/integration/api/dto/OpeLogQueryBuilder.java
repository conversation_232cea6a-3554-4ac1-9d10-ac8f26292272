package com.trinasolar.integration.api.dto;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class OpeLogQueryBuilder {
    private Query query;
    private List<Map<String, String>> sort;
    private Map<String, String> collapse;
    private Map<String, Object> aggregations;

    public OpeLogQueryBuilder() {
        this.query = new Query();
        this.sort = new ArrayList<>();
        this.collapse = new HashMap<>();
        this.aggregations = new HashMap<>();
    }

    public static class Query {
        private Bool bool;

        public Query() {
            this.bool = new Bool();
        }

        public Bool getBool() {
            return bool;
        }

        public void setBool(Bool bool) {
            this.bool = bool;
        }
    }

    public static class Bool {
        private List<Map<String, Object>> must;

        public Bool() {
            this.must = new ArrayList<>();
        }

        public List<Map<String, Object>> getMust() {
            return must;
        }

        public void setMust(List<Map<String, Object>> must) {
            this.must = must;
        }

        // 添加match_phrase条件
        public void addMatchPhrase(String field, String value) {
            Map<String, Object> matchPhrase = new HashMap<>();
            Map<String, Object> fieldMap = new HashMap<>();
            Map<String, String> queryMap = new HashMap<>();
            queryMap.put("query", value);
            fieldMap.put(field, queryMap);
            matchPhrase.put("match_phrase", fieldMap);
            must.add(matchPhrase);
        }

        // 添加query_string条件
        public void addQueryString(String value) {
            Map<String, Object> queryString = new HashMap<>();
            Map<String, Object> fieldMap = new HashMap<>();
            fieldMap.put("query", "\"" + value + "\"");
            fieldMap.put("default_field", "content");
            queryString.put("query_string", fieldMap);
            must.add(queryString);
        }

        // 添加range条件
        public void addRange(String field, Long gte, Long lt) {
            Map<String, Object> range = new HashMap<>();
            Map<String, Object> fieldMap = new HashMap<>();
            Map<String, Long> rangeMap = new HashMap<>();
            if (gte != null) {
                rangeMap.put("gte", gte);
            }
            if (lt != null) {
                rangeMap.put("lt", lt);
            }
            fieldMap.put(field, rangeMap);
            range.put("range", fieldMap);
            must.add(range);
        }
    }

    public Query getQuery() {
        return query;
    }

    public void setQuery(Query query) {
        this.query = query;
    }

    public List<Map<String, String>> getSort() {return sort;}

    public void setSort(List<Map<String, String>> sort) {
        this.sort = sort;
    }

    public Map<String, String> getCollapse() {
        return collapse;
    }

    public void setCollapse(Map<String, String> collapse) {
        this.collapse = collapse;
    }

    public Map<String, Object> getAggregations() {
        return aggregations;
    }

    public void setAggregations(Map<String, Object> aggregations) {
        this.aggregations = aggregations;
    }
    // 添加排序条件
    public void addSort(String field, String direction) {
        Map<String, String> sortItem = new HashMap<>();
        sortItem.put(field, direction);
        sort.add(sortItem);
    }

    // 添加进行折叠字段
    public void addCollapse(String fieldKey) {
        collapse.put("field", fieldKey);
    }

    // 添加聚合字段
    public void addAggregations(String fieldKey) {
        Map<String, Object> terms = new HashMap<>();
        terms.put("field", fieldKey);
        terms.put("order", new HashMap<String, String>() {{
            put("_key", "desc");
        }});
        Map<String, Object> aggregation = new HashMap<>();
        aggregation.put("terms", terms);
        aggregations.put("request_param_agg", aggregation);
    }
}
