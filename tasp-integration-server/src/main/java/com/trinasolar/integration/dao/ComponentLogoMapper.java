package com.trinasolar.integration.dao;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trinasolar.integration.api.entity.ComponentLogoDO;
import com.trinasolar.integration.controller.component.ComponentLogoBO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface ComponentLogoMapper extends BaseMapper<ComponentLogoDO> {
    List<ComponentLogoBO> componentLogoList();
}
