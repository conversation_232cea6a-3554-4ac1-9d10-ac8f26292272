package com.trinasolar.integration.controller.devops.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.apache.commons.compress.utils.Lists;

import java.util.List;
import java.util.Map;

@Schema(description = "管理后台 - DevOps项目管理保存的 VO")
@Data
@Accessors(chain = true)
@ToString(callSuper = true)
public class DevOpsProjectSaveReqVO {

    @Schema(description = "项目名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
    @NotEmpty(message = "项目名称不能为空")
    private String projectName = "";

    @Schema(description = "项目使用的模板ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
    @NotEmpty(message = "项目使用的模板ID不能为空")
    private String templateId = "100000000000002";

    @Schema(description = "项目英文名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
    @NotEmpty(message = "项目英文名称不能为空")
    private String englishNameCustom = "";

    @Schema(description = "项目类型，固定2", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
    private String typeId = "2";

    @Schema(description = "项目授权角色模板", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
    private String roleTemplate = "200000000000005,200000000000006,200000000000007";

    @Schema(description = "项目拓展属性", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
    private List<Map> props = Lists.newArrayList();

    private String testModelId = "";

    private String issueId = "";

    @Schema(description = "项目管理员", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
    private String administrator;

    @Schema(description = "项目所属部门", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
    private String deptId = "";

    @Schema(description = "项目描述", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
    private String description = "";

    @Schema(description = "父项目ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
    private String parentCode = "";

    @Schema(description = "关联需求池", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
    private String relationDemand = "";

    @Schema(description = "项目项目", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
    private String relationProject = "";
}
