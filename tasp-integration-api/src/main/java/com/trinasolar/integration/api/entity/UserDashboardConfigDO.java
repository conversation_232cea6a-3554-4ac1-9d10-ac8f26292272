package com.trinasolar.integration.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户工作台配置表
 *
 * <AUTHOR>
 */
@Data
@TableName("tasp_user_dashboard_config")
public class UserDashboardConfigDO implements Serializable {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户id
     */
    @TableField(value = "user_id")
    private String userId;

    /**
     * 模块显隐JSON
     */
    @TableField(value = "module_show")
    private String moduleShow;

    /**
     * 工作事项标签页排序JSON
     */
    @TableField(value = "work_item")
    private String workItem;

    /**
     * 创建者
     */
    @TableField(value = "creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(value = "updater")
    private String updater;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 是否删除：0：不删除，1：删除
     */
    @TableField(value = "deleted")
    private Integer deleted;
}
