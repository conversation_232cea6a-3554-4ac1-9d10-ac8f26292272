package com.trinasolar.integration.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class OpeLogResponseResult implements Serializable {
    private static final long serialVersionUID = 1L;

    private long took;
    private boolean timed_out;
    private Hits hits;
    private Aggregations aggregations;

    public long getTook() {
        return took;
    }

    public void setTook(long took) {
        this.took = took;
    }

    public boolean isTimed_out() {
        return timed_out;
    }

    public void setTimed_out(boolean timed_out) {
        this.timed_out = timed_out;
    }

    public Hits getHits() {
        return hits;
    }

    public void setHits(Hits hits) {
        this.hits = hits;
    }

    public static class Hits {
        private Total total;
        private Double max_score;
        private List<HitItem> hits;

        public Total getTotal() {
            return total;
        }

        public void setTotal(Total total) {
            this.total = total;
        }

        public Double getMax_score() {
            return max_score;
        }

        public void setMax_score(Double max_score) {
            this.max_score = max_score;
        }

        public List<HitItem> getHits() {
            return hits;
        }

        public void setHits(List<HitItem> hits) {
            this.hits = hits;
        }
    }

    public static class Total {
        private int value;
        private String relation;

        public int getValue() {
            return value;
        }

        public void setValue(int value) {
            this.value = value;
        }

        public String getRelation() {
            return relation;
        }

        public void setRelation(String relation) {
            this.relation = relation;
        }
    }

    public static class Aggregations {
        private RequestParamAgg request_param_agg;
        public RequestParamAgg getRequest_param_agg() {
            return request_param_agg;
        }
        public void setRequest_param_agg(RequestParamAgg request_param_agg) {
            this.request_param_agg = request_param_agg;
        }
    }

    public static class Buckets {
        private String key;
        private int doc_count;

        public String getKey() {
            return key;
        }
        public void setKey(String key) {
            this.key = key;
        }
        public int getDoc_count() {
            return doc_count;
        }
        public void setDoc_count(int doc_count) {
            this.doc_count = doc_count;
        }
    }

    public static class RequestParamAgg {
        private List<Buckets> buckets;
        private Integer doc_count_error_upper_bound;
        private Integer sum_other_doc_count;

        public List<Buckets> getBuckets() {
            return buckets;
        }
        public void setBuckets(List<Buckets> buckets) {
            this.buckets = buckets;
        }
        public Integer getDoc_count_error_upper_bound() {
            return doc_count_error_upper_bound;
        }
        public void setDoc_count_error_upper_bound(Integer doc_count_error_upper_bound) {
            this.doc_count_error_upper_bound = doc_count_error_upper_bound;
        }
        public Integer getSum_other_doc_count() {
            return sum_other_doc_count;
        }
        public void setSum_other_doc_count(Integer sum_other_doc_count) {
            this.sum_other_doc_count = sum_other_doc_count;
        }
    }
    public static class HitItem {
        private String _index;
        private String _id;
        private Double _score;
        private List<String> _ignored;
        private OplogDataDTO _source;
        private List<Long> sort;

        public String get_index() {
            return _index;
        }

        public void set_index(String _index) {
            this._index = _index;
        }

        public String get_id() {
            return _id;
        }

        public void set_id(String _id) {
            this._id = _id;
        }

        public Double get_score() {
            return _score;
        }

        public void set_score(Double _score) {
            this._score = _score;
        }

        public List<String> get_ignored() {
            return _ignored;
        }

        public void set_ignored(List<String> _ignored) {
            this._ignored = _ignored;
        }

        public OplogDataDTO get_source() {
            return _source;
        }

        public void set_source(OplogDataDTO _source) {
            this._source = _source;
        }

        public List<Long> getSort() {
            return sort;
        }

        public void setSort(List<Long> sort) {
            this.sort = sort;
        }
    }
}
