package com.trinasolar.integration.controller.personal;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 用户个性化配置
 *
 * <AUTHOR>
 */
@Data
public class UerPersonalConfigBO {

    /**
     * 用户ID
     */
    @NotNull
    private String userId;

    /**
     * 模块显示
     */
    private List<TagConfigBO> moduleShows;

    /**
     * 工作项显示
     */
    private List<TagConfigBO> workItems;

    /**
     * 快捷入口
     */
    private List<UserAccessConfigBO> accessConfigs;

}
