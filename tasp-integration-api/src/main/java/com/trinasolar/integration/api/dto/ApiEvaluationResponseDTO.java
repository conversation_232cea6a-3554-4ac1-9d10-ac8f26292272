package com.trinasolar.integration.api.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * API评价响应DTO
 */
@Data
public class ApiEvaluationResponseDTO {
    /**
     * 评价id
     */
    private Long id;
    
    /**
     * 用户id
     */
    private String userId;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 评分
     */
    private Integer score;
    
    /**
     * 评论内容
     */
    private String comment;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
