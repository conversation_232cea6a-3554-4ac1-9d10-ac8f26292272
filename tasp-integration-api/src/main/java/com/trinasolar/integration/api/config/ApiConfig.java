package com.trinasolar.integration.api.config;


import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import feign.Util;
import feign.codec.Decoder;
import feign.codec.EncodeException;
import feign.codec.Encoder;
import feign.form.spring.SpringFormEncoder;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.ObjectFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.cloud.openfeign.support.SpringEncoder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.lang.reflect.Type;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collection;

/**
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class ApiConfig {


    @Value("${app-market.url.uat:https://newipaas-mgr-api-uat.trinasolar.com}")
    private String uat;

    @Value("${app-market.prod.uat:https://newipaas-mgr-api-prod.trinasolar.com}")
    private String prod;

    public static final String UAT = "uat";

    // 定义全局日期格式（示例为yyyy-MM-dd HH:mm:ss）
    private static final String DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper()
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    public static final String ENV = "env";

    @Bean
    public Decoder feignDecoder() {
        return (response, type) -> {
            try {
                String bodyStr = Util.toString(response.body().asReader(Util.UTF_8));
                JavaType javaType = TypeFactory.defaultInstance().constructType(type);
                JavaTimeModule javaTimeModule = new JavaTimeModule();
                // 注册LocalDateTime序列化器和反序列化器，指定格式
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DATE_TIME_FORMAT);
                javaTimeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(formatter));
                javaTimeModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(formatter));
                OBJECT_MAPPER.registerModule(javaTimeModule);
                return OBJECT_MAPPER.readValue(bodyStr, javaType);
            } catch (Exception e) {
                throw new RuntimeException("Error decoding response", e);
            }
        };
    }

    @Bean
    public Encoder feignEncoder(ObjectFactory<HttpMessageConverters> messageConverters) {
        // 配置Feign使用UTF-8编码发送请求
        return new SpringFormEncoder(new SpringEncoder(messageConverters) {
            @Override
            public void encode(Object object, Type bodyType, RequestTemplate template) throws EncodeException {
                template.header("Content-Type", "application/json;charset=UTF-8");
                super.encode(object, bodyType, template);
            }
        });
    }

    /**
     * 根据请求头动态修改Feign的URL
     * 注意异步无法实现
     */
    @Bean
    public RequestInterceptor dynamicUrlInterceptor() {
        return template -> {
            // 从请求头或请求参数中获取env值
            String path = template.feignTarget().url();
            if (path.contains("/admin-api")) {
                String env = getEnv(template);
                try {
                    String newUrl;
                    if (UAT.equals(env)) {
                        // 构建新的完整URL，确保路径拼接正确
                        newUrl = uat + "/admin-api";
                    } else {
                        newUrl = prod + "/admin-api";
                    }
                    // 更新请求模板的URL
                    template.target(newUrl);
                    log.info("Dynamically changed Feign URL to {} based on env: {}", newUrl, env);
                } catch (Exception e) {
                    // 记录错误但不中断请求，继续使用原有URL
                    log.error("Failed to dynamically modify Feign URL: {}", e.getMessage());
                }
            }
        };
    }

    private static String getEnv(RequestTemplate template) {
        // 默认uat
        String env = "";
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) (RequestContextHolder.getRequestAttributes());
        // 从当前线程取
        if (requestAttributes != null) {
            HttpServletRequest request = requestAttributes.getRequest();
            env = request.getHeader(ENV);
            log.info("当前线程中取:{}", env);
        } else if (template.headers().get(ENV) != null) {
            // 从template中取
            Collection<String> envHeaders = template.headers().get(ENV);
            if (envHeaders != null) {
                env = envHeaders.iterator().next();
            }
            log.info("目标线程中取:{}", env);
        }
        return StringUtils.isEmpty(env) ? UAT : env;
    }
}



