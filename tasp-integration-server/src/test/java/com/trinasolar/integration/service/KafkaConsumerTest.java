package com.trinasolar.integration.service;

import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.listener.ContainerProperties;
import org.springframework.kafka.listener.KafkaMessageListenerContainer;
import org.springframework.kafka.listener.MessageListener;
import org.springframework.test.context.TestPropertySource;

/**
 * @className: KafkaConsumerTest
 * @Description: Kafka消费者测试类
 * @author: pengshy
 * @date: 2025/1/8
 */
@Slf4j
@SpringBootTest
@TestPropertySource(properties = {
    "spring.kafka.bootstrap-servers=localhost:9092",
    "spring.kafka.consumer.group-id=test-consumer-group",
    "spring.kafka.consumer.auto-offset-reset=earliest",
    "spring.kafka.consumer.key-deserializer=org.apache.kafka.common.serialization.StringDeserializer",
    "spring.kafka.consumer.value-deserializer=org.apache.kafka.common.serialization.StringDeserializer",
    "spring.kafka.consumer.enable-auto-commit=true",
    "spring.kafka.consumer.auto-commit-interval=1000"
})
class KafkaConsumerTest {

    @Autowired
    private ConsumerFactory<String, String> consumerFactory;

    /**
     * 消费executeDataShareIncrement方法发送的数据
     * 手动修改topic名称即可消费不同topic的数据
     */
   // @Test
    void testConsumeDataShareIncrementData() {
        // 手动指定要消费的topic - 可以根据实际需要修改
        String topicName = "cmdb.application_system.v1"; // 可手动修改topic名称

        log.info("开始消费executeDataShareIncrement方法发送的数据，topic: {}", topicName);

        ContainerProperties containerProps = new ContainerProperties(topicName);
        containerProps.setGroupId("test-data-share-consumer");
        containerProps.setMessageListener(new MessageListener<String, String>() {
            @Override
            public void onMessage(ConsumerRecord<String, String> record) {
                log.info("收到消息 - Topic: {}, Key: {}, Value: {}",
                    record.topic(), record.key(), record.value());
            }
        });

        KafkaMessageListenerContainer<String, String> container =
            new KafkaMessageListenerContainer<>(consumerFactory, containerProps);

        container.start();

        try {
            // 消费30秒的数据
            log.info("开始消费数据，持续30秒...");
            Thread.sleep(30000);
            log.info("消费完成");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("消费过程被中断");
        } finally {
            container.stop();
        }
    }

}
