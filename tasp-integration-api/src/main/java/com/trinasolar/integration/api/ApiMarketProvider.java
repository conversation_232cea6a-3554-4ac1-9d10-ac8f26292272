package com.trinasolar.integration.api;

import com.alibaba.fastjson.JSONObject;
import com.trinasolar.integration.api.config.ApiConfig;
import com.trinasolar.integration.api.dto.*;
import com.trinasolar.tasc.framework.common.pojo.CommonResult;
import feign.Headers;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@FeignClient(name = "app-market", path = "/admin-api", url = "${app-market.url:https://newipaas-mgr-api-uat.trinasolar.com}", configuration = ApiConfig.class)
public interface ApiMarketProvider {

    @GetMapping("/app-market/api/page")
    CommonResult<PageResultDTO> getApiCardList(@RequestHeader("token") String token,
                                               @RequestParam("name") String name,
                                               @RequestParam("apiPath") String apiPath,
                                               @RequestParam("pubOrgName") String pubOrgName,
                                               @RequestParam("createTime[0]") String startTime,
                                               @RequestParam("createTime[1]") String endTime,
                                               @RequestParam("description") String desc,
                                               @RequestParam("pageSize") Integer pageSize,
                                               @RequestParam("pageNo") Integer pageNo,
                                               @RequestParam("tagId") Integer tagId);

    @GetMapping(value = "/app-market/api/tags")
    CommonResult<List<TagDTO>> getTags(@RequestHeader("token") String token);

    @GetMapping(value = "/app-market/api/gateway")
    CommonResult<List<GatewayDTO>> getGateway(@RequestHeader("token") String token);

    @GetMapping("/app-market/api/detail")
    CommonResult<ApiDetailDTO> getDetail(@RequestHeader("token") String token, @RequestParam("id") Long id, @RequestParam("isSandbox") Boolean isSandbox);

    /**
     * 获得iPaaS所有订阅组织
     *
     * @param token
     * @return
     */
    @GetMapping("/app-market/api/org")
    CommonResult<List<OrgDTO>> getSubOrg(@RequestHeader("token") String token, @RequestParam("pubOrgId") Long pubOrgId);

    /**
     * @param token
     * @param subscribeApiDTO
     * @return
     */
    @PostMapping("/app-market/api/subscrib")
    CommonResult<ApiSubResultDTO> subscribeApi(@RequestHeader("token") String token, @RequestBody SubscribeApiDTO subscribeApiDTO);


    /**
     * 获得iPaaS指定发布组织用户,根据发布组id
     *
     * @param token
     * @return
     */
    @GetMapping("/app-market/api/puborg")
    CommonResult<PubOrgDTO> getPubOrg(@RequestHeader("token") String token, @RequestParam("code") String code, @RequestParam("pubOrgId") Long pubOrgId);


    /**
     * @param token
     * @return
     */
    @GetMapping("/app-market/api/subscribe/checkSubscribeProduct")
    CommonResult check(@RequestHeader("token") String token, @RequestParam("productId") Long productId, @RequestParam("subOrgId") Long subOrgId, @RequestParam("clientId") String clientId);


    /**
     * token
     * pageNo
     * pageSize
     * path
     * requestBody
     * responseBody
     * responseStatus
     * createTime[0]
     * createTime[1]
     *
     * @param token
     * @return
     */
    @GetMapping("/app-market/api/subscribe/logs")
    CommonResult<SubLogDTO> getSubLogs(@RequestHeader("token") String token,
                                       @RequestParam("path") String path,
                                       @RequestParam("requestBody") String requestBody,
                                       @RequestParam("responseBody") String responseBody,
                                       @RequestParam("responseStatus") String responseStatus,
                                       @RequestParam("createTime[0]") String startTime,
                                       @RequestParam("createTime[1]") String endTime,
                                       @RequestParam("pageSize") Integer pageSize,
                                       @RequestParam("pageNo") Integer pageNo);


    @GetMapping("/app-market/api/subscribe/logs/detail")
    CommonResult<LogDetailDTO> getSubLogDetail(@RequestHeader("token") String token,
                                               @RequestParam("event_id") String eventId);

    @GetMapping("/app-market/api/subscribe/getMyOrderPage")
    CommonResult<OrderPage> getMyOrderPage(@RequestHeader("token") String token, @RequestParam("productName") String productName, @RequestParam("apiPath") String apiPath,
                                           @RequestParam("pubOrgName") String pubOrgName, @RequestParam("createTime[0]") String startTime,
                                           @RequestParam("createTime[1]") String endTime, @RequestParam("pageSize") Integer pageSize, @RequestParam("pageNo") Integer pageNo);


    @PostMapping("/ipaas/product/create")
    CommonResult<Integer> publishApi(@RequestHeader("Authorization") String token, @RequestBody JSONObject jsonObject);
}