package com.trinasolar.integration.annotation;

import com.trinasolar.integration.MasterSlaveDataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.context.annotation.Import;

import java.lang.annotation.*;

/**
 * @Usage: 开启读写分离
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
@Import(MasterSlaveDataSourceAutoConfiguration.class)
public @interface EnableMasterSlaveDataSource {
}
