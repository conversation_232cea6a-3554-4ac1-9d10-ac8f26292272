package com.trinasolar.integration.controller.project.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;


@Schema(description = "应用系统新增/修改 Request VO")
@Data
public class ProjectSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "17363")
    private Long id;

    @Schema(description = "应用系统中文名", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @NotEmpty(message = "应用系统中文名不不能为空")
    private String cnName;

    @Schema(description = "应用系统英文名-用于k8s namespace和git group", requiredMode = Schema.RequiredMode.REQUIRED,  example = "Test")
    @NotEmpty(message = "应用系统英文名不能为空")
    private String enName;

    @Schema(description = "负责人", example = "赵六")
    private String masterName;

    @Schema(description = "备注", example = "随便")
    private String remark;
}
