package com.trinasolar.integration.dao;

import com.alibaba.fastjson.JSON;
import com.trinasolar.integration.api.entity.UserApp;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

/**
 * UserAppMapper 测试类
 *
 * <AUTHOR>
 * @date 2025/9/10
 */
@SpringBootTest
class UserAppMapperTest {

    @Mock
    private UserAppMapper userAppMapper;

    /**
     * 测试正常情况：根据应用ID列表查询用户应用关系
     * 验证能够正确返回匹配的用户应用数据
     */
    @Test
    void selectUserByAppIds_WithValidAppIds_ShouldReturnUserApps() {
        // Given
        List<Long> appIds = Arrays.asList(1L, 2L);
        // When
        List<UserApp> result = userAppMapper.selectUserByAppIds(appIds);
        System.out.println("result:" + JSON.toJSONString(result));
        // Then
        assertNotNull(result);
     //   assertEquals(4, result.size());

        // 验证返回的数据包含正确的应用ID
        assertTrue(result.stream().anyMatch(ua -> ua.getAppId().equals(1L)));
        assertTrue(result.stream().anyMatch(ua -> ua.getAppId().equals(2L)));

        // 验证用户代码字段被正确填充
        assertTrue(result.stream().allMatch(ua -> ua.getUserCode() != null));

        verify(userAppMapper, times(1)).selectUserByAppIds(appIds);
    }

    /**
     * 测试正常情况：根据应用ID列表查询用户应用关系
     * 验证能够正确返回匹配的用户应用数据
     */
    @Test
    void getUserAppByAppId() {

        // When
        List<UserApp> result = userAppMapper.getUserAppByAppId(1L);

        // Then
        assertNotNull(result);

        // 验证返回的数据包含正确的应用ID
        assertTrue(result.stream().anyMatch(ua -> ua.getAppId().equals(1L)));

        // 验证用户代码字段被正确填充
        assertTrue(result.stream().allMatch(ua -> ua.getUserCode() != null));

    }

}
