package com.trinasolar.integration.config.log;


import com.trinasolar.common.oplog.bean.ContextInfo;
import com.trinasolar.common.oplog.context.LogRecordContext;
import com.trinasolar.integration.api.dto.User;
import com.trinasolar.integration.util.RequestUtils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.Generated;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class OplogInterceptor implements HandlerInterceptor {
    @Generated
    public static final String HEADER_TENANT_ID = "X-Tenant-Id";
    public static final String HEADER_TRACE_ID = "X-Trace-Id";
    public static final String HEADER_VERSION_ID = "X-Version-Id";
    public static final String HEADER_TOKEN = "Authorization";
    public static final String HEADER_USER_ID = "X-User-Id";
    public static final String HEADER_USER_EMPLOYEENUMBER = "X-User-Employeenumber";
    public static final String HEADER_USER_EMAIL = "X-User-Email";
    public static final String HEADER_USER_DISPLAYNAME = "X-User-Displayname";
    public static final String HEADER_USER_DEPARTMENTNUMBER = "X-User-Departmentnumber";
    public static final String HEADER_USER_ROLE = "X-User-Role";
    public static final String HEADER_USER_PERMISSION = "X-User-Permission";
    public static final String HEADER_USER_DATASCOPE = "X-User-Datascope";

    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        try {
            String userId = "";
            String displayName = "";
            User currentUser = new User();
            try {
                currentUser = RequestUtils.getCurrentUser();
            } catch (Exception e) {
                log.info("未鉴权接口");
            }
            if (currentUser != null) {
                userId = currentUser.getId();
                displayName = currentUser.getUserRealname();
            }
            String traceId = "";
            String tenantId = "";
            ContextInfo securityContext = ContextInfo.builder().operatorId(userId).operatorName(displayName).tenantId(tenantId).traceId(traceId).build();
            LogRecordContext.setSecurityContext(securityContext);
            LogRecordContext.putVariable("user", currentUser);
        } catch (Exception e) {
            log.error("Error logging http request info", e);
        }
        return true;
    }

    public void postHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, ModelAndView modelAndView) throws Exception {
    }

    public void afterCompletion(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, Exception e) throws Exception {
    }
}

