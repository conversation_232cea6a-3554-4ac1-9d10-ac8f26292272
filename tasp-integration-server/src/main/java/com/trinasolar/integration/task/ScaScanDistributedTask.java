package com.trinasolar.integration.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trinasolar.integration.api.dto.ComponentInfo;
import com.trinasolar.integration.api.dto.SCAPage;
import com.trinasolar.integration.api.dto.ScanTaskProgressDTO;
import com.trinasolar.integration.api.dto.ScanTaskResultDTO;
import com.trinasolar.integration.api.entity.OpenSourceComptBaselineRegister;
import com.trinasolar.integration.api.enums.CheckStatusEnum;
import com.trinasolar.integration.sca.entity.SCAScanTaskRecords;
import com.trinasolar.integration.service.OpenSourceComptBaselineRegisterService;
import com.trinasolar.integration.service.SCAScanTaskRecordsService;
import com.trinasolar.integration.service.SCAService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.units.qual.A;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Spring Boot分布式定时任务
 * 处理SCA扫描任务调度
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class ScaScanDistributedTask {

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private SCAScanTaskRecordsService scanTaskRecordsService;

    @Autowired
    private OpenSourceComptBaselineRegisterService openSourceComptBaselineRegisterService;

    @Autowired
    private SCAService scaService;

    @Autowired
    @Qualifier("taskExecutor")
    ThreadPoolTaskExecutor executor;


    /**
     * 每分钟执行一次（Spring原生@Scheduled注解）
     * cron表达式格式：秒 分 时 日 月 周 [年]
     */
    @Scheduled(cron = "0 * * * * ?")
    public void executeScaScan() {
        // 1. 获取分布式锁（使用项目统一Redis前缀）
        RLock taskLock = redissonClient.getLock("tasp:sca:scan:task:lock");

        try {
            // 2. 尝试获取锁（0秒等待，30秒自动释放，防止死锁）
            boolean isLocked = taskLock.tryLock(0, 30, TimeUnit.SECONDS);
            if (isLocked) {
                // 3. 执行任务逻辑（调用现有Service层方法）
                processPendingTasks();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } finally {
            // 4. 确保锁释放（只释放当前线程持有的锁）
            if (taskLock.isHeldByCurrentThread()) {
                taskLock.unlock();
            }
        }
    }

    /**
     * 处理待执行的SCA扫描任务
     * 直接调用业务Service，符合分层架构
     */
    private void processPendingTasks() {
        log.info("=======执行的SCA状态扫描任务======");
        QueryWrapper<SCAScanTaskRecords> queryWrapper = new QueryWrapper<>();
        List<Integer> notFinishedCode = CheckStatusEnum.getNotFinishedCode();
        queryWrapper.in("check_status", notFinishedCode);
        // 查询出没有结果的扫描状态
        List<SCAScanTaskRecords> pendingTasks = scanTaskRecordsService.list(queryWrapper);
        if (CollectionUtils.isEmpty(pendingTasks)) {
            return;
        }
        // 修改异步任务加速
        for (SCAScanTaskRecords pendingTask : pendingTasks) {
            executor.execute(() -> updateTask(pendingTask));
        }
    }

    private void updateTask(SCAScanTaskRecords pendingTask) {
        Long taskId = pendingTask.getTaskId();
        ScanTaskProgressDTO scanTaskProgressDTO = scaService.scanProgress(taskId);
        Integer checkStatus = scanTaskProgressDTO.getCheckStatus();
        if (CheckStatusEnum.isFinished(checkStatus)) {
            QueryWrapper<SCAScanTaskRecords> recordsUpdateWrapper = new QueryWrapper<>();
            recordsUpdateWrapper.eq("task_id", taskId);
            SCAScanTaskRecords scaScanTaskRecords = new SCAScanTaskRecords();
            scaScanTaskRecords.setTaskId(taskId);
            scaScanTaskRecords.setCheckStatus(CheckStatusEnum.getByCode(checkStatus));
            scanTaskRecordsService.update(scaScanTaskRecords, recordsUpdateWrapper);
            try {
                if (CheckStatusEnum.FINISHED.getCode() == scanTaskProgressDTO.getCheckStatus()) {
                    // 查询任务详情组件信息
                    SCAPage<ComponentInfo> compsByTaskId = scaService.getCompsByTaskId(taskId, 1, 10000, "", 0);
                    List<ComponentInfo> componentInfos = new ArrayList<>(compsByTaskId.getRecords());
                    Map<String, String> map = componentInfos.stream().collect(Collectors.toMap(ComponentInfo::getName, ComponentInfo::getVersion, (item1, item2) -> item1));
                    LambdaQueryWrapper<OpenSourceComptBaselineRegister> open = new LambdaQueryWrapper<>();
                    open.eq(OpenSourceComptBaselineRegister::getAppId, pendingTask.getAppId());
                    List<OpenSourceComptBaselineRegister> list = openSourceComptBaselineRegisterService.list(open);
                    for (OpenSourceComptBaselineRegister openSourceComptBaselineRegister : list) {
                        String name = openSourceComptBaselineRegister.getName();
                        // 判断扫描所有结果
                        if (map.containsKey(name)) {
                            String version = map.get(name);
                            openSourceComptBaselineRegister.setUseVersion(version);
                            openSourceComptBaselineRegister.setIsUsed(1);
                        } else {
                            openSourceComptBaselineRegister.setIsBaseline(1);
                        }
                        openSourceComptBaselineRegisterService.updateById(openSourceComptBaselineRegister);
                    }
                    log.info("定时任务taskId:{},修改数据:{}条", taskId, list.size());
                }
            } catch (Exception e) {
                log.error("定时任务更新异常", e);
            }
        }
    }
}