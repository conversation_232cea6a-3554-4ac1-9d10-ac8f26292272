package com.trinasolar.integration.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.trinasolar.integration.api.dto.SystemSyncReqDTO;
import com.trinasolar.integration.api.entity.*;
import com.trinasolar.integration.api.vo.PipelineRespVO;
import com.trinasolar.integration.constants.RelationTypeConstant;
import com.trinasolar.integration.controller.projectconfig.vo.ProjectConfigSaveReqVO;
import com.trinasolar.integration.dao.AppSystemMapper;
import com.trinasolar.integration.dao.ApplicationProgramMapper;
import com.trinasolar.integration.dao.ProjectConfigMapper;
import com.trinasolar.integration.execption.AppException;
import com.trinasolar.integration.service.*;
import com.trinasolar.tasc.framework.common.pojo.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Service
public class AppServiceImpl extends BaseDevOpsService implements AppService {


    public static final String PROJECT_DEVOPS_ENV_INFO = "PROJECT_DEVOPS_ENV_INFO";
    public static final String DEVOPS_BASIC_INFO_KEY = "DEVOPS_BASIC_INFO";
    public static final String PROJECT_BASIC_INFO_KEY = "PROJECT_BASIC_INFO";
    public static final String NAMESPACE = "namespace";
    public static final String PROJECT_CODE = "projectCode";
    public static final String SPRINGCLOUD = "springcloud";

    @Autowired
    private PipelineServiceImpl pipelineService;

    @Autowired
    private ProjectConfigMapper projectConfigMapper;

    @Autowired
    private AppSystemMapper appSystemMapper;

    @Autowired
    @Qualifier("taskExecutor")
    ThreadPoolTaskExecutor executor;

    @Autowired
    private AppProgramComponentService appProgramComponentService;

    @Autowired
    private SCAService scaService;

    @Autowired
    private AppSystemInitializeService appSystemInitializeService;

    @Autowired
    private ApplicationProgramMapper pogramMapper;


    @Override
    public void createApp(ApplicationProgram program, SystemSyncReqDTO systemSyncReqDTO) {
        log.info("应用程序：{}>>>开始同步下游系统:{}", program.getProgramNameEn(), systemSyncReqDTO.getDownStreamSystem());
        boolean syncDevops = Objects.nonNull(systemSyncReqDTO)
                && systemSyncReqDTO.getDownStreamSystem().equalsIgnoreCase(RelationTypeConstant.DVEOPS);
        if (syncDevops) {
            ApplicationProgram selectOne = pogramMapper.selectOne(new LambdaQueryWrapper<ApplicationProgram>()
                    .eq(ApplicationProgram::getProgramNameEn, program.getProgramNameEn())
                    .eq(ApplicationProgram::getProgramNameCn, program.getProgramNameCn())
                    .eq(ApplicationProgram::getApplicationId, program.getApplicationId()));
            if (Objects.isNull(selectOne)) {
                log.info("应用程序不存在:{}", program.getProgramNameEn());
                return;
            } else {
                program.setId(selectOne.getId());
            }
            log.info("createApp>>>>>>>应用程序:{}", JSON.toJSONString(selectOne));
            PageResult<PipelineRespVO> pipelines = pipelineService.getPipelines(program.getId());
            if (Objects.nonNull(pipelines) && pipelines.getTotal() > 0) {
                log.info("应用程序已经同步过devops:{}", program.getProgramNameEn());
            }
            log.info("开始初始化流水线:{}", program.getProgramNameEn());
            //获取环境配置信息
            String programeNameEn = program.getProgramNameEn();
            String gitUrl = program.getGitlabRepoUrl();
            //应用系统ID
            Long applicationId = program.getApplicationId();
            //技术栈（前端、后端）
            String technologyStack = program.getTechnologyStack();
            AppSystem appSystem = appSystemMapper.selectById(applicationId);
            String systemSimpleEnName = appSystem.getEnSimpleName();
            //获取项目配置信息
            List<ProjectConfigDO> projectConfigs = projectConfigMapper.selectByProjectId(applicationId);
            String envJsonStr = projectConfigs.stream().filter(e -> PROJECT_DEVOPS_ENV_INFO.equals(e.getConfigKey())).findFirst().get().getConfigContent();
            String devopsBasicJsonStr = projectConfigs.stream().filter(e -> DEVOPS_BASIC_INFO_KEY.equals(e.getConfigKey())).findFirst().get().getConfigContent();
            String projectBasicJsonStr = projectConfigs.stream().filter(e -> PROJECT_BASIC_INFO_KEY.equals(e.getConfigKey())).findFirst().get().getConfigContent();
            JSONObject devJson = JSONUtil.parseObj(devopsBasicJsonStr);
            JSONObject proJson = JSONUtil.parseObj(projectBasicJsonStr);
            JSONObject envJson = JSONUtil.parseObj(envJsonStr);
            //devops的projectID
            String devOpsCode = devJson.getStr(PROJECT_CODE);
            if (StrUtil.isEmpty(devOpsCode)) {
                log.error("devops的projectID不存在，无法初始化流水线: {}", programeNameEn);
                throw new AppException("devops的projectID不存在，无法初始化流水线");
            }
            // 项目空间和git地址进行关联
            if (StrUtil.isEmpty(gitUrl)) {
                log.error("代码库地址不存在，无法初始化流水线: {}", programeNameEn);
                return;
            }
            String gitHashId = relatedGitRepo(devOpsCode, gitUrl);
            if (StrUtil.isEmpty(gitHashId)) {
                log.error("代码库关联失败，无法初始化流水线: {}", programeNameEn);
                throw new AppException("gitHashId不存在，流水线代码库关联失败");
            }

            String businessCode = proJson.getStr(NAMESPACE);
            if (StrUtil.isEmpty(businessCode)) {
                log.error("缺失业务域，无法初始化流水线: {}", programeNameEn);
                throw new AppException("缺失业务域，无法初始化流水线");
            }
            //初始化流水线
            String[] environments = {"DEV", "TEST", "UAT", "PROD"};

            for (String env : environments) {
                executor.execute(() -> {
                    pipelineService.importPipeline(envJson, systemSimpleEnName, programeNameEn, devOpsCode, env, businessCode,
                            gitHashId, technologyStack, getSessionCookie());
                });
            }
            //更新初始化状态
            program.setInitStatus("1");
            pogramMapper.updateById(program);

            //插入gitlab与应用程序关联数据
            AppProgramComponent appProgramComponent = new AppProgramComponent();
            appProgramComponent.setProgramId(program.getId());
            appProgramComponent.setComponent(RelationTypeConstant.GITLAB);
            appProgramComponent.setComponentId(gitHashId);
            appProgramComponent.setSystemId(applicationId);
            appProgramComponentService.save(appProgramComponent);
        }

        //初始化SCA
        if (Objects.nonNull(systemSyncReqDTO) && systemSyncReqDTO.getDownStreamSystem().equalsIgnoreCase(RelationTypeConstant.SCA)) {
            doProgramScaInit(program);
        }
    }

    @Override
    public Boolean initProgramGit() {
        List<ApplicationProgram> applicationPrograms = pogramMapper.selectList();
        applicationPrograms.forEach(applicationProgram -> {
            String gitlabRepoUrl = applicationProgram.getGitlabRepoUrl();
            if (StrUtil.isNotEmpty(gitlabRepoUrl)) {
                ProjectConfigSaveReqVO projectConfigSaveReqVO = new ProjectConfigSaveReqVO();
                projectConfigSaveReqVO.setProjectId(applicationProgram.getApplicationId());
                projectConfigSaveReqVO.setConfigKey("DEVOPS_BASIC_INFO");
                ProjectConfigDO projectConfigDO = projectConfigMapper.selectByProjectIdAndConfigKey(projectConfigSaveReqVO);
                JSONObject proJson = JSONUtil.parseObj(projectConfigDO.getConfigContent());
                String str = proJson.getStr("projectCode");
                String gitId = getGitId(str, gitlabRepoUrl);
                if (StrUtil.isEmpty(gitId)) {
                    return;
                }
                AppProgramComponent appProgramComponent = new AppProgramComponent();
                appProgramComponent.setProgramId(applicationProgram.getId());
                appProgramComponent.setComponent(RelationTypeConstant.GITLAB);
                appProgramComponent.setComponentId(gitId);
                appProgramComponent.setSystemId(applicationProgram.getApplicationId());
                boolean save = appProgramComponentService.save(appProgramComponent);
                log.info("初始化gitlab关联数据:{}", save);
            }
        });
        return true;
    }

    /**
     * 应用程序创建sca模板
     *
     * @param program
     * @return
     */
    private synchronized boolean doProgramScaInit(ApplicationProgram program) {
        ApplicationProgram selectOne = pogramMapper.selectOne(new LambdaQueryWrapper<ApplicationProgram>()
                .eq(ApplicationProgram::getProgramNameEn, program.getProgramNameEn())
                .eq(ApplicationProgram::getProgramNameCn, program.getProgramNameCn())
                .eq(ApplicationProgram::getApplicationId, program.getApplicationId()));
        if (Objects.isNull(selectOne)) {
            log.info("应用程序不存在:{}", program.getProgramNameEn());
            return false;
        } else {
            program.setId(selectOne.getId());
        }
        log.info("createApp>>>>>>>应用程序:{}", JSON.toJSONString(selectOne));
        if (StrUtil.isEmpty(program.getGitlabRepoUrl())) {
            log.error("代码库地址为空，无法初始化SCA模块！");
            return false;
        }
        log.info("开始初始化SCA模块:{}", program.getProgramNameEn());
        Long applicationId = program.getApplicationId();
        AppSystemInitialize one = appSystemInitializeService.getOne(new LambdaQueryWrapper<AppSystemInitialize>()
                .eq(AppSystemInitialize::getAppId, applicationId)
                .eq(AppSystemInitialize::getRelationType, RelationTypeConstant.SCA));
        if ((one == null && StrUtil.isEmpty(one.getRelationId()))) {
            log.error("应用系统SCA未同步，program Name: {}", program.getProgramNameCn());
            throw new AppException("应用系统SCA未同步");
        }
        if ("null".equals(one.getRelationId())) {
            log.error("应用系统SCA初始化失败，program Name: {}", program.getProgramNameCn());
            return false;
        }

        boolean exists = appProgramComponentService.exists(new LambdaQueryWrapper<AppProgramComponent>()
                .eq(AppProgramComponent::getProgramId, program.getId())
                .eq(AppProgramComponent::getComponent, RelationTypeConstant.SCA));
        if (exists) {
            log.info("SCA模块已经初始化，无需再次初始化:{}", program.getProgramNameEn());
            return true;
        }

        Integer modelId = scaService.createModel(program, Integer.valueOf(one.getRelationId()));
        if (Objects.nonNull(modelId)) {
            log.info("SCA模块初始化成功:{}", program.getProgramNameEn());
            AppProgramComponent appProgramComponent = new AppProgramComponent();
            appProgramComponent.setProgramId(program.getId());
            appProgramComponent.setComponent(RelationTypeConstant.SCA);
            appProgramComponent.setComponentId(String.valueOf(modelId));
            appProgramComponent.setProjectId(one.getRelationId());
            appProgramComponent.setSystemId(applicationId);
            return appProgramComponentService.save(appProgramComponent);
        }
        return false;
    }

    private String getAliasName(String gitUrl) {
        // 正则匹配所有协议格式（http/https/ssh）和端口号，提取仓库路径
        // 调整后的正则处理（行号~828附近）
        String regex = "^(?:https?|ssh)://(?:[^/]+@)?[^/:]+(?::\\d+)?/(.+?)(?:\\.git)?$";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(gitUrl);
        if (matcher.find()) {
            // 直接返回完整路径，不进行二次分割
            return matcher.group(1);
        }
        return "";
    }

    private String relatedGitRepo(String projectCode, String gitUrl) {
        if (StrUtil.isEmpty(gitUrl)) {
            log.error("代码库地址不存在，无法初始化流水线");
            return "";
        }

        String relatedGitUrl = DOMAIN_HOST + "/ms/repository/api/user/repositories/[projectId]/";
        String aliasName = getAliasName(gitUrl);
        String searchGitUrl = DOMAIN_HOST + "/ms/repository/api/user/repositories/" + projectCode + "/search?aliasName=" + aliasName + "&page=1&pageSize=20&sortBy=&sortType=";
        try (HttpResponse searchResp = HttpRequest.get(searchGitUrl).cookie(getSessionCookie()).execute()) {
            if (searchResp.getStatus() == 200) {
                String searchData = searchResp.body();
                if (!StrUtil.isEmpty(searchData)) {
                    JSONObject json = JSONUtil.parseObj(searchData);
                    if (json.getInt("status") == 0) {
                        int countExistGit = json.getJSONObject("data").getInt("count");
                        if (countExistGit == 1) {
                            //已存在，返回已存在的HashID
                            JSONObject result = json.getJSONObject("data").getJSONArray("records").getJSONObject(0);
                            return result.getStr("repositoryHashId");
                        }

                    }
                }
            }
        } catch (Exception e) {
            log.error("查询代码库关联失败", e);
        }
        String formData = "{\n" +
                "    \"@type\": \"gitlab\",\n" +
                "    \"aliasName\": \"" + aliasName + "\",\n" +
                "    \"credentialId\": \"new_gitlab.global\",\n" +
                "    \"projectName\": \"" + aliasName + "\",\n" +
                "    \"url\": \"" + gitUrl + "\",\n" +
                "    \"authType\": \"HTTP\",\n" +
                "    \"svnType\": \"ssh\",\n" +
                "    \"tenant\": \"\",\n" +
                "    \"userName\": \"121100\"\n" +
                "}";
        try {
            HttpResponse response = HttpRequest
                    .post(relatedGitUrl.replace("[projectId]", projectCode))
                    .cookie(getSessionCookie())
                    .contentType("application/json")
                    .body(formData).execute();
            if (response.getStatus() == 200) {
                String result = response.body();
                if (!StrUtil.isEmpty(result)) {
                    JSONObject json = JSONUtil.parseObj(result);
                    if (json.getInt("status") == 0) {
                        //返回新建的hashId
                        return json.getJSONObject("data").getStr("hashId");
                    }
                }
            }
        } catch (Exception e) {
            log.error("代码库关联失败", e);
        }
        return "";
    }

    private String getGitId(String projectCode, String gitUrl) {

        String aliasName = getAliasName(gitUrl);
        String searchGitUrl = DOMAIN_HOST + "/ms/repository/api/user/repositories/" + projectCode + "/search?aliasName=" + aliasName + "&page=1&pageSize=20&sortBy=&sortType=";
        try (HttpResponse searchResp = HttpRequest.get(searchGitUrl).cookie(getSessionCookie()).execute()) {
            if (searchResp.getStatus() == 200) {
                String searchData = searchResp.body();
                if (!StrUtil.isEmpty(searchData)) {
                    JSONObject json = JSONUtil.parseObj(searchData);
                    if (json.getInt("status") == 0) {
                        int countExistGit = json.getJSONObject("data").getInt("count");
                        if (countExistGit == 1) {
                            //已存在，返回已存在的HashID
                            JSONObject result = json.getJSONObject("data").getJSONArray("records").getJSONObject(0);
                            return result.getStr("repositoryHashId");
                        }

                    }
                }
            }
        } catch (Exception e) {
            log.error("查询代码库关联失败", e);
        }
        return "";
    }
}

