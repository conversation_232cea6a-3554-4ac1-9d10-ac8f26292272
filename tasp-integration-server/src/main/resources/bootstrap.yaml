server:
  port: 80
spring:
  main:
    allow-circular-references: true
  profiles:
    active: dev
  application:
    name: kepler-integration
  # MVC配置
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  # Web资源配置
  web:
    resources:
      add-mappings: true
  config:
    import: optional:nacos:${spring.application.name}.yaml
  # 云服务配置
  cloud:
    # Nacos服务发现配置
    nacos:
      discovery:
        server-addr: ${NACOS_HOST:***********}:${NACOS_PORT:8848}
        username: ${NACOS_USERNAME:nacos}
        password: ${NACOS_PASSWORD:LUOcheng@384}
        namespace: ${NACOS_NS:dev}
        logging:
          default-config-enabled: false
      # Nacos配置中心配置
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        username: ${spring.cloud.nacos.discovery.username}
        password: ${spring.cloud.nacos.discovery.password}
        file-extension: yaml
        namespace: ${NACOS_NS:dev}
        group: DEFAULT_GROUP
        name: ${spring.application.name}
        logging:
          default-config-enabled: false

# 日志配置
logging:
  config: classpath:logback-spring.xml
  level:
    com.alibaba.nacos: WARN
# Nacos 日志配置 - 完全禁用默认配置
nacos:
  logging:
    default-config-enabled: false

# Swagger配置
swagger:
  enabled: true

# OpenAPI 配置
project:
  openapi:
    name: "TASP Development Service"
    version: "v1.0"
    description: "开发服务API文档"
    group: "development"
    homepage:
      enable: true
      path: "/"

