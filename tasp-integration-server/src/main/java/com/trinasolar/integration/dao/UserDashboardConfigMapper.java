package com.trinasolar.integration.dao;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trinasolar.integration.api.entity.UserAccessConfigDO;
import com.trinasolar.integration.api.entity.UserDashboardConfigDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 */
@Mapper
public interface UserDashboardConfigMapper extends BaseMapper<UserDashboardConfigDO> {
    int deleteByUserId(String userId);

    UserDashboardConfigDO selectByUserId(String userId);
}
