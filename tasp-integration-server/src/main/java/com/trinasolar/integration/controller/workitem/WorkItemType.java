package com.trinasolar.integration.controller.workitem;

/**
 * 工作项类型枚举类
 *
 * <AUTHOR>
 */
public enum WorkItemType {
    PAAS("1", "PaaS"),
    STORAGE("2", "存储"),
    DOCUMENT("3", "文档"),
    API("4", "API"),
    NEED("5", "需求"),
    TASK("6", "任务"),
    BUG("7", "缺陷"),
    TIS("8", "TIS工单"),
    CODE_MERGE("9", "代码合并请求");

    private final String code;
    private final String info;

    WorkItemType(String code, String info) {
        this.code = code;
        this.info = info;
    }

    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }

    public static String getInfoByCode(String code) {
        for (WorkItemType e : WorkItemType.values()) {
            if (e.getCode().equals(code)) {
                return e.getInfo();
            }
        }
        return null;
    }
}
