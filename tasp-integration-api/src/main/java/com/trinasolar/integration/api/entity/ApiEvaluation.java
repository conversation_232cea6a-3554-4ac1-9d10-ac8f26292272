package com.trinasolar.integration.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * API评价实体类
 * 存储用户对API的评分和评论信息
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName("api_evaluation") // 对应数据库表名
public class ApiEvaluation {
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 用户id，与ApiSubscribeRecord中的userId关联
     */
    private String userId;
    
    /**
     * API id，与ApiSubscribeRecord中的productId关联
     */
    private Long productId;
    
    /**
     * API名称
     */
    private String productName;
    
    /**
     * 评分（1-5分）
     */
    private Integer score;
    
    /**
     * 评论内容（最多200字符）
     */
    private String comment;
    
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}