package com.trinasolar.integration.controller.personal;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 用户个人详情
 *
 * <AUTHOR>
 */
@Data
public class UerPersonalInfoBO {

    /**
     * 应用系统数量
     */
    private Integer systemNum;

    /**
     * 应用程序数量
     */
    private Integer programNum;

    /**
     * 快捷入口
     */
    private List<UserAccessConfigBO> accessConfigs;


    /**
     * 最近使用应用系统
     */
    private List<AppSystemBO> appSystems;

    /**
     * 最近使用应用程序
     */
    private List<AppProgramBO> appPrograms;

    /**
     * 文档浏览记录
     */
    private List<DocumentBrowseBO> documentBrowses;

    /**
     * 统计模块区域
     */
    private Map<String, List<String>> statModule;
}
