package com.trinasolar.integration.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.trinasolar.integration.api.entity.ShareComponentDO;
import com.trinasolar.integration.controller.component.ShareComponentBO;
import com.trinasolar.integration.controller.component.ShareComponentReq;
import com.trinasolar.integration.controller.component.ShareComponentVO;
import jakarta.validation.constraints.NotNull;


public interface ShareComponentService extends IService<ShareComponentDO> {
    Page<ShareComponentVO> shareComponentPage(Page<ShareComponentVO> page, ShareComponentReq req);

    Boolean addShareComponent(ShareComponentBO shareComponentBO);

    boolean checkComponentNameExists(@NotNull(message = "组件名称不能为空") String componentName, Long id);

    Boolean updateShareComponent(ShareComponentBO shareComponentBO);

    Boolean deleteShareComponent(Long id);

    Boolean updateStatus(Long id, Integer status);

    boolean checkUpdateComponentNameExists(String componentName, Long id);
}
