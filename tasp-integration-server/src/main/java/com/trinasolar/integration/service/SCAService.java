package com.trinasolar.integration.service;

import com.alibaba.fastjson.JSONObject;
import com.trinasolar.integration.api.dto.*;
import com.trinasolar.integration.api.entity.ApplicationProgram;
import com.trinasolar.integration.api.entity.UserApp;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface SCAService {
    /**
     * 生成并维护签名参数
     *
     * @param payloadObject 请求体
     * @param requestParams 请求入场
     * @return 响应是入参请求头参数
     */
    Map<String, String> generateSignatureParams(String method, JSONObject payloadObject, Map<String, Object> requestParams);

    ScaResponseDTO<ScaDataDTO> getModules(Long projectId);

    SCAPage<ScaTaskDetailDTO> getModulesScanHistory(Long id, Integer pageNo, Integer pageSize);

    SCAPage<ComponentInfo> getCompsByTaskId(Long taskId, Integer pageNo, Integer pageSize, String pattern, Integer source);

    ScanTaskResultDTO getCompsByTaskId(Long taskId);

    Integer createProjects(String systemCnName, List<UserApp> userApps);

    boolean updateProjects(Long scaProjectId, String systemCnName, List<UserApp> userApps);

    ScaResponseDTO<ScaProjectDTO> getProjectDtail(Long scaProjectId);

    boolean deleteProjects(Long sysId);

    Integer createModel(ApplicationProgram program, Integer projectId);

    boolean deleteModel(Long appId);

    ScanTaskDTO scan(Long appId);

    ScanTaskProgressDTO scanProgress(Long taskId);

}
