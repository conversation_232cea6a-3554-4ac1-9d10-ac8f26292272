package com.trinasolar.integration.config;

import com.trinasolar.tasc.framework.common.pojo.CommonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * <AUTHOR>
 */
@Slf4j
@RestControllerAdvice(basePackages = "com.trinasolar.integration.controller.sca")
public class SCAExceptionHandler {

    @ExceptionHandler(Exception.class)
    public CommonResult<Void> handleGlobalException(Exception ex) {
        log.error("SCA市场异常", ex);
        return CommonResult.error(1, ex.getMessage());
    }
}