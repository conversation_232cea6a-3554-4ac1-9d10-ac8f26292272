package com.trinasolar.integration;

import com.github.xiaoymin.knife4j.spring.configuration.Knife4jAutoConfiguration;
import com.trinasolar.integration.annotation.EnableMasterSlaveDataSource;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <AUTHOR>
 */
@EnableMasterSlaveDataSource
@EnableAsync
@EnableDiscoveryClient
@EnableScheduling
@SpringBootApplication(scanBasePackages = "com.trinasolar.integration",
        exclude = {
                DataSourceAutoConfiguration.class // 排除默认的数据源自动配置
                ,Knife4jAutoConfiguration.class
        })
@EnableFeignClients(basePackages = {"com.trinasolar.integration.api"})
public class KeplerIntegrationServerApplication {

    public static void main(String[] args) {
        // 禁用 Nacos 内部日志配置，避免 CONFIG_LOG_FILE 冲突
        System.setProperty("nacos.logging.default.config.enabled", "false");
        System.setProperty("nacos.logging.config", "");
        System.setProperty("com.alibaba.nacos.naming.log.level", "WARN");
        System.setProperty("com.alibaba.nacos.config.log.level", "WARN");
        SpringApplication.run(KeplerIntegrationServerApplication.class, args);
    }

}
