package com.trinasolar.integration.controller.personal;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PageReq implements Serializable {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "页数")
	private Integer page = 1;

	@ApiModelProperty(value = "分页大小")
	private Integer size = 10;

}
